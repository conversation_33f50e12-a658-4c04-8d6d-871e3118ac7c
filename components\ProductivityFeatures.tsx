'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  Zap,
  Upload,
  Download,
  Code,
  Settings,
  Workflow,
  FileText,
  Database,
  Link,
  CheckCircle,
  Clock,
  BarChart3,
  AlertCircle,
  X
} from 'lucide-react';
import { analytics } from '@/lib/analytics';
import { processBatchFiles, validateFile, generateBatchResultsFile, BatchProcessingResult } from '@/lib/fileProcessor';
import { IntegrationManager, GoogleDocsIntegration, NotionIntegration, IntegrationResult } from '@/lib/integrations';

interface ProductivityFeaturesProps {
  onFeatureUse: (feature: string, data: any) => void;
}

export default function ProductivityFeatures({ onFeatureUse }: ProductivityFeaturesProps) {
  const [activeTab, setActiveTab] = useState('batch');
  const [batchFiles, setBatchFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [batchResults, setBatchResults] = useState<BatchProcessingResult | null>(null);
  const [fileErrors, setFileErrors] = useState<{[key: string]: string}>({});
  const [apiKey, setApiKey] = useState('');
  const [integrationManager] = useState(() => new IntegrationManager());
  const [connectedIntegrations, setConnectedIntegrations] = useState<string[]>([]);
  const [integrationStatus, setIntegrationStatus] = useState<{[key: string]: string}>({});

  const handleBatchUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const validFiles: File[] = [];
    const errors: {[key: string]: string} = {};

    // Validate each file
    files.forEach(file => {
      const validation = validateFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors[file.name] = validation.error || 'Invalid file';
      }
    });

    setBatchFiles(validFiles);
    setFileErrors(errors);
    setBatchResults(null); // Clear previous results
    analytics.trackShare('batch_upload');
  };

  const processBatchFiles = async () => {
    if (batchFiles.length === 0) return;

    setIsProcessing(true);
    setProcessingProgress(0);
    setBatchResults(null);

    try {
      // Process files with real file processor
      const results = await processBatchFiles(
        batchFiles,
        {
          intensity: 'medium',
          style: 'balanced',
          preserveFormat: true,
          addVariations: false
        },
        (completed, total) => {
          setProcessingProgress((completed / total) * 100);
        }
      );

      setBatchResults(results);

      onFeatureUse('batch_processing', {
        filesProcessed: results.totalFiles,
        successfulFiles: results.successfulFiles,
        failedFiles: results.failedFiles,
        totalTime: results.totalProcessingTime,
        avgImprovementScore: results.averageImprovementScore
      });

      analytics.trackShare('batch_processing_completed');
    } catch (error) {
      console.error('Batch processing failed:', error);
    } finally {
      setIsProcessing(false);
      setProcessingProgress(0);
    }
  };

  const removeFile = (index: number) => {
    const newFiles = batchFiles.filter((_, i) => i !== index);
    setBatchFiles(newFiles);
    setBatchResults(null); // Clear results when files change
  };

  const downloadResults = () => {
    if (!batchResults) return;

    const content = generateBatchResultsFile(batchResults);
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `ghostlayer-batch-results-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    analytics.trackShare('batch_results_downloaded');
  };

  const connectIntegration = async (integrationName: string) => {
    setIntegrationStatus(prev => ({ ...prev, [integrationName]: 'connecting' }));

    try {
      const result = await integrationManager.connectIntegration(integrationName);

      if (result.success) {
        setConnectedIntegrations(prev => [...prev, integrationName]);
        setIntegrationStatus(prev => ({ ...prev, [integrationName]: 'connected' }));
        analytics.trackShare(`${integrationName}_connected`);
      } else {
        setIntegrationStatus(prev => ({ ...prev, [integrationName]: 'error' }));
      }

      return result;
    } catch (error) {
      setIntegrationStatus(prev => ({ ...prev, [integrationName]: 'error' }));
      return { success: false, message: 'Connection failed', error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  const testIntegration = async (integrationName: string) => {
    const integration = integrationManager.getIntegration(integrationName);
    if (!integration) return;

    try {
      let result: IntegrationResult;

      if (integrationName === 'google-docs') {
        result = await integration.createHumanizedDocument(
          'GhostLayer Test Document',
          'This is a test document created by GhostLayer to verify the Google Docs integration is working correctly.',
          { intensity: 'medium', style: 'balanced', preserveFormat: true, addVariations: false }
        );
      } else if (integrationName === 'notion') {
        result = await integration.createHumanizedPage(
          'GhostLayer Test Page',
          'This is a test page created by GhostLayer to verify the Notion integration is working correctly.',
          { intensity: 'medium', style: 'balanced', preserveFormat: true, addVariations: false }
        );
      } else {
        return;
      }

      onFeatureUse(`${integrationName}_test`, result.data);
      analytics.trackShare(`${integrationName}_tested`);

      return result;
    } catch (error) {
      console.error(`${integrationName} test failed:`, error);
    }
  };

  const generateApiKey = () => {
    const newApiKey = 'gl_' + Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
    setApiKey(newApiKey);
    analytics.trackShare('api_key_generated');
  };

  return (
    <Card className="bg-white/5 backdrop-blur-lg border-white/10">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-white">
          <div className="p-2 bg-green-500/20 rounded-lg">
            <Zap className="w-5 h-5 text-green-400" />
          </div>
          <div>
            <h3 className="text-lg font-semibold">Productivity & Enterprise</h3>
            <p className="text-gray-400 text-sm font-normal">Advanced tools for power users and teams</p>
          </div>
        </CardTitle>

        {/* Enterprise Features Explanation */}
        <div className="mt-4 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
          <h4 className="text-yellow-300 font-semibold mb-2">🏢 Productivity & Enterprise Benefits</h4>
          <p className="text-gray-300 text-sm leading-relaxed mb-3">
            Our Enterprise tier is designed for teams, businesses, and organizations that need advanced humanization
            capabilities at scale. Perfect for content teams, marketing agencies, and educational institutions.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <h5 className="text-white font-medium mb-2">🚀 Scale & Performance</h5>
              <ul className="text-gray-300 space-y-1">
                <li>• Unlimited processing</li>
                <li>• Priority processing queues</li>
                <li>• Batch operations</li>
                <li>• Advanced analytics</li>
              </ul>
            </div>
            <div>
              <h5 className="text-white font-medium mb-2">👥 Team Collaboration</h5>
              <ul className="text-gray-300 space-y-1">
                <li>• Multi-user accounts</li>
                <li>• Shared workspaces</li>
                <li>• Role-based permissions</li>
                <li>• Team usage analytics</li>
              </ul>
            </div>
            <div>
              <h5 className="text-white font-medium mb-2">🔧 Integration & Support</h5>
              <ul className="text-gray-300 space-y-1">
                <li>• API access & webhooks</li>
                <li>• Custom integrations</li>
                <li>• Priority support</li>
                <li>• Training & onboarding</li>
              </ul>
            </div>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3 bg-slate-800/50">
            <TabsTrigger value="batch" className="text-white">📁 Batch</TabsTrigger>
            <TabsTrigger value="api" className="text-white">🔌 API</TabsTrigger>
            <TabsTrigger value="integrations" className="text-white">🔗 Integrations</TabsTrigger>
          </TabsList>

          {/* Batch Processing */}
          <TabsContent value="batch" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-white font-semibold">Batch Processing</h4>
                <Badge className="bg-green-500/20 text-green-400">
                  Enterprise Feature
                </Badge>
              </div>
              
              <div className="p-4 bg-white/5 rounded-lg border-2 border-dashed border-gray-600">
                <div className="text-center">
                  <Upload className="w-8 h-8 text-gray-400 mx-auto mb-3" />
                  <p className="text-gray-300 mb-3">Upload multiple files for batch humanization</p>
                  <input
                    type="file"
                    multiple
                    accept=".txt,.md,.docx,.pdf,.rtf,.odt"
                    onChange={handleBatchUpload}
                    className="hidden"
                    id="batch-upload"
                  />
                  <label htmlFor="batch-upload">
                    <Button className="bg-blue-600 hover:bg-blue-700 cursor-pointer">
                      <Upload className="w-4 h-4 mr-2" />
                      Select Files
                    </Button>
                  </label>
                </div>
              </div>

              {/* File Validation Errors */}
              {Object.keys(fileErrors).length > 0 && (
                <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="w-4 h-4 text-red-400" />
                    <h5 className="text-red-300 font-medium">File Validation Errors</h5>
                  </div>
                  <div className="space-y-1">
                    {Object.entries(fileErrors).map(([fileName, error]) => (
                      <div key={fileName} className="text-sm text-red-200">
                        <strong>{fileName}:</strong> {error}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {batchFiles.length > 0 && (
                <div className="space-y-3">
                  <h5 className="text-white font-medium">Selected Files ({batchFiles.length})</h5>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {batchFiles.map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white/5 rounded">
                        <div className="flex items-center gap-2">
                          <FileText className="w-4 h-4 text-blue-400" />
                          <div className="flex flex-col">
                            <span className="text-gray-300 text-sm">{file.name}</span>
                            <span className="text-gray-500 text-xs">{file.type || 'Unknown type'}</span>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-400 text-xs">
                            {(file.size / 1024).toFixed(1)} KB
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeFile(index)}
                            className="h-6 w-6 p-0 text-gray-400 hover:text-red-400"
                          >
                            <X className="w-3 h-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <Button
                    onClick={processBatchFiles}
                    disabled={isProcessing}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    {isProcessing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing... {processingProgress.toFixed(0)}%
                      </>
                    ) : (
                      <>
                        <Zap className="w-4 h-4 mr-2" />
                        Process All Files
                      </>
                    )}
                  </Button>

                  {/* Progress Bar */}
                  {isProcessing && (
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${processingProgress}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              )}

              {/* Batch Results */}
              {batchResults && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h5 className="text-white font-medium">Processing Results</h5>
                    <Button
                      onClick={downloadResults}
                      className="bg-blue-600 hover:bg-blue-700"
                      size="sm"
                    >
                      <Download className="w-4 h-4 mr-2" />
                      Download Results
                    </Button>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="p-3 bg-green-500/10 border border-green-500/20 rounded-lg text-center">
                      <div className="text-2xl font-bold text-green-400">{batchResults.successfulFiles}</div>
                      <div className="text-gray-400 text-sm">Successful</div>
                    </div>
                    <div className="p-3 bg-red-500/10 border border-red-500/20 rounded-lg text-center">
                      <div className="text-2xl font-bold text-red-400">{batchResults.failedFiles}</div>
                      <div className="text-gray-400 text-sm">Failed</div>
                    </div>
                    <div className="p-3 bg-blue-500/10 border border-blue-500/20 rounded-lg text-center">
                      <div className="text-2xl font-bold text-blue-400">{batchResults.averageImprovementScore.toFixed(1)}%</div>
                      <div className="text-gray-400 text-sm">Avg Score</div>
                    </div>
                    <div className="p-3 bg-purple-500/10 border border-purple-500/20 rounded-lg text-center">
                      <div className="text-2xl font-bold text-purple-400">{(batchResults.totalProcessingTime / 1000).toFixed(1)}s</div>
                      <div className="text-gray-400 text-sm">Total Time</div>
                    </div>
                  </div>

                  {/* Individual File Results */}
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {batchResults.results.map((result, index) => (
                      <div key={index} className={`p-3 rounded-lg border ${result.error ? 'bg-red-500/10 border-red-500/20' : 'bg-green-500/10 border-green-500/20'}`}>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            {result.error ? (
                              <AlertCircle className="w-4 h-4 text-red-400" />
                            ) : (
                              <CheckCircle className="w-4 h-4 text-green-400" />
                            )}
                            <span className="text-white text-sm font-medium">{result.fileName}</span>
                          </div>
                          {!result.error && (
                            <span className="text-gray-400 text-sm">{result.result.improvementScore}% improvement</span>
                          )}
                        </div>
                        {result.error && (
                          <p className="text-red-300 text-sm mt-1">{result.error}</p>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="grid grid-cols-3 gap-4">
                <div className="p-3 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-green-400">50+</div>
                  <div className="text-gray-400 text-sm">File Formats</div>
                </div>
                <div className="p-3 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-blue-400">10GB</div>
                  <div className="text-gray-400 text-sm">Max Size</div>
                </div>
                <div className="p-3 bg-white/5 rounded-lg text-center">
                  <div className="text-2xl font-bold text-purple-400">24/7</div>
                  <div className="text-gray-400 text-sm">Processing</div>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* API Access */}
          <TabsContent value="api" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-white font-semibold">API Access</h4>
                <Badge className="bg-purple-500/20 text-purple-400">
                  Developer Tools
                </Badge>
              </div>

              {/* API Explanation */}
              <div className="p-4 bg-purple-500/10 border border-purple-500/20 rounded-lg">
                <h5 className="text-purple-300 font-semibold mb-2">🔌 What is API Access?</h5>
                <p className="text-gray-300 text-sm leading-relaxed mb-3">
                  The GhostLayer API allows developers to integrate our humanization technology directly into their applications,
                  websites, and workflows. Perfect for content management systems, writing tools, and automated workflows.
                </p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                  <div>
                    <h6 className="text-white font-medium mb-1">Features:</h6>
                    <ul className="text-gray-300 space-y-1">
                      <li>• RESTful API endpoints</li>
                      <li>• Real-time text processing</li>
                      <li>• Multiple output formats</li>
                      <li>• Webhook support</li>
                    </ul>
                  </div>
                  <div>
                    <h6 className="text-white font-medium mb-1">Use Cases:</h6>
                    <ul className="text-gray-300 space-y-1">
                      <li>• Content management systems</li>
                      <li>• Writing assistance tools</li>
                      <li>• Automated content workflows</li>
                      <li>• Third-party integrations</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-white/5 rounded-lg">
                <h5 className="text-white font-medium mb-3">API Key</h5>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={apiKey}
                    placeholder="Generate an API key to get started"
                    readOnly
                    className="flex-1 p-2 bg-black/30 border border-gray-600 rounded text-white text-sm"
                  />
                  <Button onClick={generateApiKey} className="bg-purple-600 hover:bg-purple-700">
                    <Code className="w-4 h-4 mr-2" />
                    Generate
                  </Button>
                </div>
              </div>

              <div className="p-4 bg-black/30 rounded-lg">
                <h5 className="text-white font-medium mb-3">Example Usage</h5>
                <pre className="text-green-400 text-sm overflow-x-auto">
{`curl -X POST https://api.ghostlayer.app/v1/humanize \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "Your AI-generated text here",
    "style": "academic",
    "intensity": "medium",
    "language": "en"
  }'`}
                </pre>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <BarChart3 className="w-4 h-4 text-blue-400" />
                    <span className="text-white font-medium">Rate Limits</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-300">
                    <div>Free: 100 requests/day</div>
                    <div>Pro: 10,000 requests/day</div>
                    <div>Enterprise: Unlimited</div>
                  </div>
                </div>
                
                <div className="p-4 bg-white/5 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-green-400" />
                    <span className="text-white font-medium">Response Time</span>
                  </div>
                  <div className="space-y-1 text-sm text-gray-300">
                    <div>Average: 1.2s</div>
                    <div>99th percentile: 3.5s</div>
                    <div>Uptime: 99.9%</div>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Database className="w-4 h-4 text-blue-400" />
                  <span className="text-blue-400 font-semibold text-sm">SDK Libraries</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  Official SDKs available for popular programming languages
                </p>
                <div className="flex gap-2 flex-wrap">
                  {['JavaScript', 'Python', 'PHP', 'Ruby', 'Go', 'Java'].map((lang) => (
                    <Badge key={lang} variant="secondary" className="bg-blue-500/20 text-blue-400">
                      {lang}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Integrations */}
          <TabsContent value="integrations" className="mt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h4 className="text-white font-semibold">Tool Integrations</h4>
                <Badge className="bg-orange-500/20 text-orange-400">
                  Coming Soon
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-4">
                {[
                  { name: 'Google Docs', key: 'google-docs', icon: '📄', status: 'available', description: 'Humanize directly in Google Docs' },
                  { name: 'Notion', key: 'notion', icon: '📝', status: 'available', description: 'Seamless Notion integration' },
                  { name: 'WordPress', key: 'wordpress', icon: '🌐', status: 'beta', description: 'WordPress plugin for content creators' },
                  { name: 'Slack', key: 'slack', icon: '💬', status: 'coming', description: 'Team collaboration in Slack' },
                  { name: 'Microsoft Word', key: 'word', icon: '📘', status: 'coming', description: 'Word add-in for Office users' },
                  { name: 'Zapier', key: 'zapier', icon: '⚡', status: 'coming', description: 'Automate with 5000+ apps' }
                ].map((integration) => {
                  const isConnected = connectedIntegrations.includes(integration.key);
                  const status = integrationStatus[integration.key];
                  const isAvailable = integration.status === 'available';

                  return (
                    <div key={integration.name} className="p-4 bg-white/5 rounded-lg">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="text-2xl">{integration.icon}</span>
                        <div className="flex-1">
                          <div className="text-white font-medium">{integration.name}</div>
                          <div className="flex items-center gap-2">
                            <Badge className={
                              isConnected ? 'bg-green-500/20 text-green-400' :
                              integration.status === 'available' ? 'bg-blue-500/20 text-blue-400' :
                              integration.status === 'beta' ? 'bg-yellow-500/20 text-yellow-400' :
                              'bg-gray-500/20 text-gray-400'
                            }>
                              {isConnected ? 'Connected' :
                               integration.status === 'available' ? 'Available' :
                               integration.status === 'beta' ? 'Beta' : 'Coming Soon'}
                            </Badge>
                            {status === 'connecting' && (
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-400"></div>
                            )}
                          </div>
                        </div>
                      </div>
                      <p className="text-gray-400 text-sm mb-3">{integration.description}</p>

                      {isAvailable && (
                        <div className="flex gap-2">
                          <Button
                            onClick={() => connectIntegration(integration.key)}
                            disabled={status === 'connecting' || isConnected}
                            className={`flex-1 ${isConnected ? 'bg-green-600 hover:bg-green-700' : 'bg-blue-600 hover:bg-blue-700'}`}
                            size="sm"
                          >
                            {status === 'connecting' ? (
                              <>
                                <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                                Connecting...
                              </>
                            ) : isConnected ? (
                              <>
                                <CheckCircle className="w-3 h-3 mr-2" />
                                Connected
                              </>
                            ) : (
                              <>
                                <Link className="w-3 h-3 mr-2" />
                                Connect
                              </>
                            )}
                          </Button>

                          {isConnected && (
                            <Button
                              onClick={() => testIntegration(integration.key)}
                              className="bg-purple-600 hover:bg-purple-700"
                              size="sm"
                            >
                              <Zap className="w-3 h-3 mr-2" />
                              Test
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>

              <div className="p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 rounded-lg border border-orange-500/20">
                <div className="flex items-center gap-2 mb-2">
                  <Workflow className="w-4 h-4 text-orange-400" />
                  <span className="text-orange-400 font-semibold text-sm">Workflow Automation</span>
                </div>
                <p className="text-gray-300 text-sm mb-3">
                  Set up automated workflows to humanize content as it's created across your favorite tools.
                </p>
                <Button className="bg-orange-600 hover:bg-orange-700">
                  <Settings className="w-4 h-4 mr-2" />
                  Configure Workflows
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
