# 🚨 Google OAuth Error Diagnosis & Fix

## Current Error: "Access blocked: This app's request is invalid"

### Root Cause Analysis
The error occurs because of **missing or incorrect OAuth configuration**. Based on your current setup:

1. ✅ **GOOGLE_CLIENT_ID**: Correctly set to `637401884201-7jro0haaqqs8acdkvd3rac7mopojca36.apps.googleusercontent.com`
2. ❌ **GOOGLE_CLIENT_SECRET**: Still using placeholder value
3. ❌ **NEXTAUTH_SECRET**: Still using placeholder value
4. ❓ **OAuth Consent Screen**: May not be properly configured

## 🔧 Step-by-Step Fix

### Step 1: Generate NEXTAUTH_SECRET
```bash
# Run this command in your terminal to generate a secure secret
openssl rand -base64 32
```

Copy the output and replace the placeholder in `.env.local`:
```env
NEXTAUTH_SECRET=<paste-the-generated-secret-here>
```

### Step 2: Get Google Client Secret
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to "APIs & Services" → "Credentials"
3. Find your OAuth 2.0 Client ID: `637401884201-7jro0haaqqs8acdkvd3rac7mopojca36.apps.googleusercontent.com`
4. Click on it to view details
5. Copy the **Client Secret** value
6. Update `.env.local`:
```env
GOOGLE_CLIENT_SECRET=<paste-your-actual-client-secret-here>
```

### Step 3: Verify OAuth Consent Screen Configuration
1. In Google Cloud Console, go to "APIs & Services" → "OAuth consent screen"
2. Ensure these settings are correct:

**App Information:**
```
App name: GhostLayer
User support email: <your-email>
Developer contact information: <your-email>
```

**Authorized domains:**
```
localhost (for development)
```

**Scopes:**
- `../auth/userinfo.email`
- `../auth/userinfo.profile`
- `openid`

### Step 4: Check Authorized Redirect URIs
1. Go to "APIs & Services" → "Credentials"
2. Click on your OAuth 2.0 Client ID
3. Under "Authorized redirect URIs", ensure you have:
```
http://localhost:3000/api/auth/callback/google
```

### Step 5: Add Test Users (Development Only)
1. In "OAuth consent screen" → "Test users"
2. Add your email address as a test user
3. Click "Save"

### Step 6: Restart Development Server
```bash
# Stop the current server (Ctrl+C)
# Then restart
npm run dev
```

## 🧪 Testing the Fix

1. Open your app at `http://localhost:3000`
2. Click "Sign in with Google"
3. You should see the Google OAuth consent screen
4. Grant permissions
5. You should be redirected back to your app successfully

## 🚨 Common Issues & Solutions

### Issue: "redirect_uri_mismatch"
**Solution**: Ensure the redirect URI is exactly:
```
http://localhost:3000/api/auth/callback/google
```

### Issue: "invalid_client"
**Solution**: Double-check that:
- Client ID is correct
- Client Secret is correct (not placeholder)
- No extra spaces or characters

### Issue: "access_denied"
**Solution**: 
- Add your email to test users
- Ensure OAuth consent screen is properly filled out

### Issue: Still getting "Access blocked"
**Solution**: 
1. Check if your Google account has 2FA enabled
2. Try using an incognito/private browser window
3. Clear browser cookies for localhost:3000
4. Verify the OAuth consent screen status is not "Needs verification"

## 🔍 Debug Mode
Your NextAuth is configured with debug mode in development. Check the browser console and terminal for detailed error messages.

## 📋 Quick Checklist
- [ ] NEXTAUTH_SECRET generated and set
- [ ] GOOGLE_CLIENT_SECRET obtained and set
- [ ] OAuth consent screen configured
- [ ] Redirect URI added: `http://localhost:3000/api/auth/callback/google`
- [ ] Test user added (your email)
- [ ] Development server restarted
- [ ] Tested in clean browser session

## 🆘 If Still Not Working

1. **Check Google Cloud Console Logs**:
   - Go to "Logging" in Google Cloud Console
   - Look for OAuth-related errors

2. **Enable Additional Logging**:
   Add to `.env.local`:
   ```env
   NEXTAUTH_DEBUG=true
   ```

3. **Verify API Enablement**:
   - Ensure "Google+ API" is enabled in Google Cloud Console
   - Go to "APIs & Services" → "Library" → Search "Google+" → Enable

4. **Contact Support**:
   If all else fails, the issue might be with Google's OAuth service or your Google account settings.

## 🔄 Next Steps After Fix
Once OAuth is working:
1. Set up production OAuth credentials
2. Configure database for user storage
3. Implement session management
4. Add user roles and permissions
