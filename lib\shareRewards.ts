// Share reward system for premium unlocks and viral referrals
import { analytics } from './analytics';

export interface ShareReward {
  type: 'credits' | 'premium_unlock' | 'feature_unlock';
  amount?: number;
  feature?: string;
  duration?: number; // in days for temporary unlocks
  description: string;
}

export interface ShareActivity {
  platform: string;
  timestamp: number;
  rewarded: boolean;
  rewardType?: string;
}

export interface UserShareStats {
  totalShares: number;
  platformShares: { [platform: string]: number };
  creditsEarned: number;
  premiumUnlocked: boolean;
  premiumUnlockExpiry?: number;
  shareStreak: number;
  lastShareDate?: number;
}

// Share reward configuration
const SHARE_REWARDS = {
  // First share rewards
  first_share: {
    type: 'credits' as const,
    amount: 25,
    description: 'Welcome bonus for your first share!'
  },
  
  // Platform-specific rewards
  twitter: {
    type: 'credits' as const,
    amount: 10,
    description: 'Twitter share bonus'
  },
  
  linkedin: {
    type: 'credits' as const,
    amount: 15,
    description: 'LinkedIn professional share bonus'
  },
  
  facebook: {
    type: 'credits' as const,
    amount: 10,
    description: 'Facebook share bonus'
  },
  
  // Milestone rewards
  share_milestone_5: {
    type: 'premium_unlock' as const,
    duration: 7, // 7 days
    description: '7-day Premium unlock for 5 shares!'
  },
  
  share_milestone_10: {
    type: 'credits' as const,
    amount: 100,
    description: 'Big bonus for 10 shares!'
  },
  
  share_milestone_25: {
    type: 'premium_unlock' as const,
    duration: 30, // 30 days
    description: '30-day Premium unlock for 25 shares!'
  },
  
  // Streak rewards
  share_streak_3: {
    type: 'feature_unlock' as const,
    feature: 'heavy_intensity',
    duration: 3,
    description: '3-day Heavy intensity unlock for 3-day streak!'
  },
  
  share_streak_7: {
    type: 'premium_unlock' as const,
    duration: 14,
    description: '14-day Premium unlock for 7-day streak!'
  },
  
  // Viral rewards (when shares get engagement)
  viral_share: {
    type: 'credits' as const,
    amount: 50,
    description: 'Viral bonus for high-engagement share!'
  }
};

// Share reward manager
export class ShareRewardManager {
  private static instance: ShareRewardManager;
  private userStats: Map<string, UserShareStats> = new Map();
  private shareHistory: Map<string, ShareActivity[]> = new Map();

  static getInstance(): ShareRewardManager {
    if (!ShareRewardManager.instance) {
      ShareRewardManager.instance = new ShareRewardManager();
    }
    return ShareRewardManager.instance;
  }

  // Initialize user stats
  private initializeUserStats(userId: string): UserShareStats {
    const stats: UserShareStats = {
      totalShares: 0,
      platformShares: {},
      creditsEarned: 0,
      premiumUnlocked: false,
      shareStreak: 0
    };
    this.userStats.set(userId, stats);
    return stats;
  }

  // Get user share stats
  getUserStats(userId: string): UserShareStats {
    return this.userStats.get(userId) || this.initializeUserStats(userId);
  }

  // Process a share and award rewards
  async processShare(userId: string, platform: string, shareData?: any): Promise<ShareReward[]> {
    const stats = this.getUserStats(userId);
    const history = this.shareHistory.get(userId) || [];
    const now = Date.now();
    
    // Add to share history
    const shareActivity: ShareActivity = {
      platform,
      timestamp: now,
      rewarded: false
    };
    history.push(shareActivity);
    this.shareHistory.set(userId, history);
    
    // Update stats
    stats.totalShares++;
    stats.platformShares[platform] = (stats.platformShares[platform] || 0) + 1;
    
    // Update share streak
    this.updateShareStreak(userId, now);
    
    // Calculate rewards
    const rewards: ShareReward[] = [];
    
    // First share bonus
    if (stats.totalShares === 1) {
      rewards.push(SHARE_REWARDS.first_share);
    }
    
    // Platform-specific rewards (limit to once per day per platform)
    const todayShares = history.filter(h => 
      h.platform === platform && 
      now - h.timestamp < 24 * 60 * 60 * 1000
    );
    
    if (todayShares.length === 1 && SHARE_REWARDS[platform as keyof typeof SHARE_REWARDS]) {
      rewards.push(SHARE_REWARDS[platform as keyof typeof SHARE_REWARDS]);
    }
    
    // Milestone rewards
    if (stats.totalShares === 5) {
      rewards.push(SHARE_REWARDS.share_milestone_5);
    } else if (stats.totalShares === 10) {
      rewards.push(SHARE_REWARDS.share_milestone_10);
    } else if (stats.totalShares === 25) {
      rewards.push(SHARE_REWARDS.share_milestone_25);
    }
    
    // Streak rewards
    if (stats.shareStreak === 3) {
      rewards.push(SHARE_REWARDS.share_streak_3);
    } else if (stats.shareStreak === 7) {
      rewards.push(SHARE_REWARDS.share_streak_7);
    }
    
    // Apply rewards
    await this.applyRewards(userId, rewards);
    
    // Mark share as rewarded
    shareActivity.rewarded = true;
    shareActivity.rewardType = rewards.map(r => r.type).join(',');
    
    // Track analytics
    analytics.trackShare(platform, {
      userId,
      totalShares: stats.totalShares,
      rewardsEarned: rewards.length,
      shareStreak: stats.shareStreak
    });
    
    return rewards;
  }

  // Update share streak
  private updateShareStreak(userId: string, timestamp: number) {
    const stats = this.getUserStats(userId);
    const oneDayMs = 24 * 60 * 60 * 1000;
    
    if (!stats.lastShareDate) {
      stats.shareStreak = 1;
    } else {
      const daysSinceLastShare = Math.floor((timestamp - stats.lastShareDate) / oneDayMs);
      
      if (daysSinceLastShare === 1) {
        // Consecutive day
        stats.shareStreak++;
      } else if (daysSinceLastShare > 1) {
        // Streak broken
        stats.shareStreak = 1;
      }
      // Same day shares don't affect streak
    }
    
    stats.lastShareDate = timestamp;
  }

  // Apply rewards to user
  private async applyRewards(userId: string, rewards: ShareReward[]) {
    const stats = this.getUserStats(userId);
    
    for (const reward of rewards) {
      switch (reward.type) {
        case 'credits':
          stats.creditsEarned += reward.amount || 0;
          // In production, this would update the database
          break;
          
        case 'premium_unlock':
          stats.premiumUnlocked = true;
          if (reward.duration) {
            const expiryTime = Date.now() + (reward.duration * 24 * 60 * 60 * 1000);
            stats.premiumUnlockExpiry = expiryTime;
          }
          break;
          
        case 'feature_unlock':
          // Store feature unlock in user preferences
          // This would be handled by the user management system
          break;
      }
    }
  }

  // Check if user has premium access (including temporary unlocks)
  hasPremiumAccess(userId: string): boolean {
    const stats = this.getUserStats(userId);
    
    if (!stats.premiumUnlocked) return false;
    
    // Check if temporary unlock has expired
    if (stats.premiumUnlockExpiry && Date.now() > stats.premiumUnlockExpiry) {
      stats.premiumUnlocked = false;
      stats.premiumUnlockExpiry = undefined;
      return false;
    }
    
    return true;
  }

  // Get remaining premium time
  getPremiumTimeRemaining(userId: string): number {
    const stats = this.getUserStats(userId);
    
    if (!stats.premiumUnlocked || !stats.premiumUnlockExpiry) return 0;
    
    const remaining = stats.premiumUnlockExpiry - Date.now();
    return Math.max(0, remaining);
  }

  // Generate share incentive message
  getShareIncentiveMessage(userId: string): string {
    const stats = this.getUserStats(userId);
    
    if (stats.totalShares === 0) {
      return "🎉 Share your first transformation and get 25 bonus credits!";
    } else if (stats.totalShares < 5) {
      const remaining = 5 - stats.totalShares;
      return `🔥 ${remaining} more shares to unlock 7 days of Premium features!`;
    } else if (stats.totalShares < 10) {
      const remaining = 10 - stats.totalShares;
      return `💎 ${remaining} more shares to earn 100 bonus credits!`;
    } else if (stats.totalShares < 25) {
      const remaining = 25 - stats.totalShares;
      return `👑 ${remaining} more shares to unlock 30 days of Premium!`;
    } else {
      return "🌟 Keep sharing to earn more credits and help others discover GhostLayer!";
    }
  }

  // Get next milestone info
  getNextMilestone(userId: string): { shares: number; reward: string } | null {
    const stats = this.getUserStats(userId);
    
    if (stats.totalShares < 5) {
      return { shares: 5, reward: "7-day Premium unlock" };
    } else if (stats.totalShares < 10) {
      return { shares: 10, reward: "100 bonus credits" };
    } else if (stats.totalShares < 25) {
      return { shares: 25, reward: "30-day Premium unlock" };
    }
    
    return null;
  }

  // Simulate viral engagement (would be called by webhook in production)
  async processViralEngagement(userId: string, shareId: string, engagementScore: number) {
    if (engagementScore > 100) { // High engagement threshold
      const rewards = [SHARE_REWARDS.viral_share];
      await this.applyRewards(userId, rewards);
      
      analytics.trackShare('viral_bonus', {
        userId,
        shareId,
        engagementScore,
        rewardAmount: rewards[0].amount
      });
      
      return rewards;
    }
    
    return [];
  }
}

// Export singleton instance
export const shareRewardManager = ShareRewardManager.getInstance();
