#!/usr/bin/env node

/**
 * Generate a secure NEXTAUTH_SECRET for the GhostLayer application
 * This script generates a cryptographically secure random string
 * suitable for use as NEXTAUTH_SECRET in .env.local
 */

const crypto = require('crypto');

function generateSecureSecret() {
  // Generate 32 bytes of random data and encode as base64
  const secret = crypto.randomBytes(32).toString('base64');
  return secret;
}

function main() {
  console.log('🔐 GhostLayer Auth Secret Generator');
  console.log('=====================================\n');
  
  const secret = generateSecureSecret();
  
  console.log('Generated NEXTAUTH_SECRET:');
  console.log(`NEXTAUTH_SECRET=${secret}\n`);
  
  console.log('📋 Instructions:');
  console.log('1. Copy the NEXTAUTH_SECRET value above');
  console.log('2. Replace the placeholder value in .env.local');
  console.log('3. Restart your development server');
  console.log('4. Get your GOOGLE_CLIENT_SECRET from Google Cloud Console\n');
  
  console.log('🔗 Google Cloud Console:');
  console.log('https://console.cloud.google.com/apis/credentials\n');
  
  console.log('✅ Your OAuth setup will be complete once both secrets are configured!');
}

if (require.main === module) {
  main();
}

module.exports = { generateSecureSecret };
