// Test script to verify capitalization preservation
const { processTextDirectly } = require('./lib/textProcessor');

// Test cases for capitalization preservation
const testCases = [
  {
    input: "AI is transforming the world",
    expected: "AI", // Should preserve "AI" capitalization
    description: "Basic AI abbreviation"
  },
  {
    input: "The LLM model uses API calls",
    expected: ["LLM", "API"], // Should preserve both
    description: "Multiple abbreviations"
  },
  {
    input: "JavaScript and HTML are web technologies",
    expected: ["JavaScript", "HTML"], // Should preserve mixed case and abbreviations
    description: "Mixed case and abbreviations"
  },
  {
    input: "OpenAI's GPT-4 model",
    expected: ["OpenAI", "GPT-4"], // Should preserve company names and version numbers
    description: "Company names and versions"
  }
];

console.log("🧪 Testing Capitalization Preservation\n");

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`Input: "${testCase.input}"`);
  
  try {
    const result = processTextDirectly(testCase.input, {
      intensity: 'medium',
      style: 'balanced',
      preserveFormat: true,
      addVariations: false
    });
    
    console.log(`Output: "${result.humanizedText}"`);
    
    // Check if expected terms are preserved
    const expectedTerms = Array.isArray(testCase.expected) ? testCase.expected : [testCase.expected];
    const preserved = expectedTerms.every(term => result.humanizedText.includes(term));
    
    console.log(`✅ Preservation: ${preserved ? 'PASSED' : 'FAILED'}`);
    
    if (!preserved) {
      console.log(`❌ Expected terms not found: ${expectedTerms.join(', ')}`);
    }
    
  } catch (error) {
    console.log(`❌ Error: ${error.message}`);
  }
  
  console.log('---\n');
});

// Additional debug test
console.log("🔍 Debug Test - Step by Step Protection");
const debugInput = "AI and LLM are important";
console.log(`Debug Input: "${debugInput}"`);

// Test the protection function directly
try {
  const { protectCapitalizationAndTerms } = require('./lib/textProcessor');
  
  const protectedTerms = [
    'AI', 'API', 'URL', 'HTML', 'CSS', 'JavaScript', 'TypeScript', 'React', 'Next.js',
    'LLM', 'LLMs', 'GPT', 'ChatGPT', 'OpenAI', 'GitHub', 'LinkedIn', 'B2B', 'UI', 'UX'
  ];
  
  const { protectedResult, protectionMap } = protectCapitalizationAndTerms(debugInput, protectedTerms);
  
  console.log(`Protected Result: "${protectedResult}"`);
  console.log(`Protection Map:`, Array.from(protectionMap.entries()));
  
  // Test restoration
  let restored = protectedResult;
  protectionMap.forEach((original, placeholder) => {
    restored = restored.replace(new RegExp(placeholder, 'g'), original);
  });
  
  console.log(`Restored Result: "${restored}"`);
  
} catch (error) {
  console.log(`❌ Debug Error: ${error.message}`);
}
