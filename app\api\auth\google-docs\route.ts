import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { db } from '@/lib/database';

/**
 * Google Docs OAuth callback handler
 * This route handles the OAuth callback from Google when users connect Google Docs
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized. Please sign in first.' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const code = searchParams.get('code');
    const error = searchParams.get('error');
    const state = searchParams.get('state');

    // Handle OAuth errors
    if (error) {
      console.error('Google Docs OAuth error:', error);
      return NextResponse.redirect(
        new URL(`/?error=google_docs_auth_failed&message=${encodeURIComponent(error)}`, request.url)
      );
    }

    // <PERSON>le missing authorization code
    if (!code) {
      return NextResponse.json(
        { error: 'Missing authorization code' },
        { status: 400 }
      );
    }

    // Exchange authorization code for access token
    const tokenResponse = await fetch('https://oauth2.googleapis.com/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: process.env.GOOGLE_CLIENT_ID || '',
        client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
        code,
        grant_type: 'authorization_code',
        redirect_uri: `${process.env.NEXTAUTH_URL}/api/auth/google-docs`,
      }),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.text();
      console.error('Token exchange failed:', errorData);
      return NextResponse.json(
        { error: 'Failed to exchange authorization code for token' },
        { status: 500 }
      );
    }

    const tokenData = await tokenResponse.json();

    // Store tokens in database associated with the user's session
    console.log('Google Docs tokens received:', {
      access_token: tokenData.access_token ? 'present' : 'missing',
      refresh_token: tokenData.refresh_token ? 'present' : 'missing',
      expires_in: tokenData.expires_in,
      scope: tokenData.scope,
    });

    // Save tokens to database
    try {
      await db.saveGoogleDocsToken(session.user.id, {
        accessToken: tokenData.access_token,
        refreshToken: tokenData.refresh_token,
        expiresIn: tokenData.expires_in,
        scope: tokenData.scope
      });
      console.log('Google Docs tokens saved successfully for user:', session.user.id);
    } catch (error) {
      console.error('Failed to save Google Docs tokens:', error);
      return NextResponse.json(
        { error: 'Failed to save authentication tokens' },
        { status: 500 }
      );
    }
    
    // Redirect back to the app with success message
    return NextResponse.redirect(
      new URL('/?google_docs_connected=true', request.url)
    );

  } catch (error) {
    console.error('Google Docs OAuth callback error:', error);
    return NextResponse.json(
      { error: 'Internal server error during Google Docs authentication' },
      { status: 500 }
    );
  }
}

/**
 * Handle POST requests for manual token refresh or connection testing
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'test_connection':
        // Test the Google Docs API connection
        // In a real implementation, you would use stored tokens to make an API call
        return NextResponse.json({
          success: true,
          message: 'Google Docs connection test successful',
          data: {
            connected: true,
            scopes: ['https://www.googleapis.com/auth/documents'],
            user: session.user.email,
          }
        });

      case 'disconnect':
        // Revoke tokens and disconnect Google Docs
        // In a real implementation, you would remove tokens from database
        return NextResponse.json({
          success: true,
          message: 'Google Docs disconnected successfully'
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Google Docs API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
