/**
 * Heavy Mode Time-Based Access Policy
 * Manages temporary free access to Heavy transformation intensity mode
 */

// Set the end date for free Heavy mode access (1 month from today)
const HEAVY_MODE_FREE_END_DATE = new Date('2025-02-26T23:59:59Z'); // 1 month from current date

export interface HeavyModeAccessInfo {
  hasAccess: boolean;
  isPermanent: boolean;
  isTemporaryFree: boolean;
  timeRemaining?: number;
  daysRemaining?: number;
  hoursRemaining?: number;
  endDate?: Date;
  reason: 'premium' | 'temporary_free' | 'expired' | 'not_logged_in';
}

/**
 * Check if user has access to Heavy mode
 */
export function checkHeavyModeAccess(session: any): HeavyModeAccessInfo {
  const now = new Date();
  const isPremium = session?.user?.tier === 'premium' || session?.user?.tier === 'pro';
  const isLoggedIn = !!session?.user;

  // If user is not logged in, no access
  if (!isLoggedIn) {
    return {
      hasAccess: false,
      isPermanent: false,
      isTemporaryFree: false,
      reason: 'not_logged_in'
    };
  }

  // If user is premium, they always have access
  if (isPremium) {
    return {
      hasAccess: true,
      isPermanent: true,
      isTemporaryFree: false,
      reason: 'premium'
    };
  }

  // Check if temporary free access period is still active
  if (now <= HEAVY_MODE_FREE_END_DATE) {
    const timeRemaining = HEAVY_MODE_FREE_END_DATE.getTime() - now.getTime();
    const daysRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60 * 24));
    const hoursRemaining = Math.ceil(timeRemaining / (1000 * 60 * 60));

    return {
      hasAccess: true,
      isPermanent: false,
      isTemporaryFree: true,
      timeRemaining,
      daysRemaining,
      hoursRemaining,
      endDate: HEAVY_MODE_FREE_END_DATE,
      reason: 'temporary_free'
    };
  }

  // Temporary access has expired
  return {
    hasAccess: false,
    isPermanent: false,
    isTemporaryFree: false,
    reason: 'expired'
  };
}

/**
 * Get user-friendly message about Heavy mode access
 */
export function getHeavyModeAccessMessage(accessInfo: HeavyModeAccessInfo): string {
  switch (accessInfo.reason) {
    case 'premium':
      return 'Premium access - Heavy mode always available';
    
    case 'temporary_free':
      if (accessInfo.daysRemaining && accessInfo.daysRemaining > 1) {
        return `Free access ends in ${accessInfo.daysRemaining} days`;
      } else if (accessInfo.hoursRemaining && accessInfo.hoursRemaining > 1) {
        return `Free access ends in ${accessInfo.hoursRemaining} hours`;
      } else {
        return 'Free access ends soon';
      }
    
    case 'expired':
      return 'Free access period ended - Upgrade to Premium for Heavy mode';
    
    case 'not_logged_in':
      return 'Sign in to access Heavy mode';
    
    default:
      return 'Heavy mode not available';
  }
}

/**
 * Get countdown display for temporary access
 */
export function getCountdownDisplay(accessInfo: HeavyModeAccessInfo): string | null {
  if (!accessInfo.isTemporaryFree || !accessInfo.timeRemaining) {
    return null;
  }

  const days = Math.floor(accessInfo.timeRemaining / (1000 * 60 * 60 * 24));
  const hours = Math.floor((accessInfo.timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((accessInfo.timeRemaining % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) {
    return `${days}d ${hours}h`;
  } else if (hours > 0) {
    return `${hours}h ${minutes}m`;
  } else {
    return `${minutes}m`;
  }
}

/**
 * Get badge color for access status
 */
export function getAccessBadgeColor(accessInfo: HeavyModeAccessInfo): string {
  switch (accessInfo.reason) {
    case 'premium':
      return 'bg-purple-500/20 text-purple-400';
    case 'temporary_free':
      return 'bg-green-500/20 text-green-400';
    case 'expired':
      return 'bg-red-500/20 text-red-400';
    default:
      return 'bg-gray-500/20 text-gray-400';
  }
}

/**
 * Check if we should show upgrade prompt
 */
export function shouldShowUpgradePrompt(accessInfo: HeavyModeAccessInfo): boolean {
  return accessInfo.reason === 'expired' || 
         (accessInfo.isTemporaryFree && accessInfo.daysRemaining && accessInfo.daysRemaining <= 3);
}

/**
 * Get upgrade prompt message
 */
export function getUpgradePromptMessage(accessInfo: HeavyModeAccessInfo): string {
  if (accessInfo.reason === 'expired') {
    return 'Heavy mode is now premium-only. Upgrade to continue using advanced humanization.';
  }
  
  if (accessInfo.isTemporaryFree && accessInfo.daysRemaining && accessInfo.daysRemaining <= 3) {
    return `Heavy mode becomes premium-only in ${accessInfo.daysRemaining} days. Upgrade now to secure access.`;
  }
  
  return 'Upgrade to Premium for unlimited Heavy mode access.';
}
