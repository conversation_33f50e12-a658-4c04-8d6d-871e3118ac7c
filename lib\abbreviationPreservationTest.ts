import { processTextDirectly } from './textProcessor';
import { ProcessingOptions } from '@/types';

/**
 * Test suite for abbreviation preservation in humanization
 * This helps verify that abbreviations are properly maintained during text processing
 */

interface AbbreviationTest {
  input: string;
  expectedAbbreviations: string[];
  description: string;
}

const abbreviationTests: AbbreviationTest[] = [
  {
    input: "The AI system uses API calls to process data efficiently.",
    expectedAbbreviations: ["AI", "API"],
    description: "Basic tech abbreviations"
  },
  {
    input: "Our CEO announced that the CTO will lead the new IT initiative.",
    expectedAbbreviations: ["CEO", "CTO", "IT"],
    description: "Business role abbreviations"
  },
  {
    input: "The HTML and CSS files need to be optimized for better UI/UX.",
    expectedAbbreviations: ["HTML", "CSS", "UI", "UX"],
    description: "Web development abbreviations"
  },
  {
    input: "NASA's research on AI and machine learning helps improve GPS accuracy.",
    expectedAbbreviations: ["NASA", "AI", "GPS"],
    description: "Mixed organization and tech abbreviations"
  },
  {
    input: "The B2B platform integrates with REST APIs and uses JSON for data exchange.",
    expectedAbbreviations: ["B2B", "REST", "APIs", "JSON"],
    description: "Business and technical abbreviations"
  },
  {
    input: "Our R&D team developed an SDK for iOS and Android apps.",
    expectedAbbreviations: ["R&D", "SDK", "iOS"],
    description: "Abbreviations with special characters"
  },
  {
    input: "The 401k plan and IRA accounts offer tax advantages for employees.",
    expectedAbbreviations: ["401k", "IRA"],
    description: "Financial abbreviations with numbers"
  },
  {
    input: "The PhD candidate studied AI algorithms for her dissertation.",
    expectedAbbreviations: ["PhD", "AI"],
    description: "Academic and tech abbreviations"
  }
];

export function testAbbreviationPreservation(): {
  passed: number;
  failed: number;
  results: Array<{
    test: AbbreviationTest;
    result: string;
    preservedAbbreviations: string[];
    missingAbbreviations: string[];
    passed: boolean;
  }>;
} {
  const options: ProcessingOptions = {
    intensity: 'medium',
    style: 'balanced',
    language: 'en'
  };

  let passed = 0;
  let failed = 0;
  const results: Array<{
    test: AbbreviationTest;
    result: string;
    preservedAbbreviations: string[];
    missingAbbreviations: string[];
    passed: boolean;
  }> = [];

  abbreviationTests.forEach(test => {
    const result = processTextDirectly(test.input, options);
    
    // Extract abbreviations from the result
    const preservedAbbreviations = test.expectedAbbreviations.filter(abbrev => {
      const regex = new RegExp(`\\b${abbrev}\\b`, 'g');
      return regex.test(result.humanizedText);
    });

    const missingAbbreviations = test.expectedAbbreviations.filter(abbrev => {
      const regex = new RegExp(`\\b${abbrev}\\b`, 'g');
      return !regex.test(result.humanizedText);
    });

    const testPassed = missingAbbreviations.length === 0;
    
    if (testPassed) {
      passed++;
    } else {
      failed++;
    }

    results.push({
      test,
      result: result.humanizedText,
      preservedAbbreviations,
      missingAbbreviations,
      passed: testPassed
    });
  });

  return { passed, failed, results };
}

export function runAbbreviationTest(): void {
  console.log('🧪 Running Abbreviation Preservation Tests...\n');
  
  const testResults = testAbbreviationPreservation();
  
  console.log(`📊 Test Results: ${testResults.passed} passed, ${testResults.failed} failed\n`);
  
  testResults.results.forEach((result, index) => {
    const status = result.passed ? '✅' : '❌';
    console.log(`${status} Test ${index + 1}: ${result.test.description}`);
    console.log(`   Input: "${result.test.input}"`);
    console.log(`   Output: "${result.result}"`);
    console.log(`   Expected: [${result.test.expectedAbbreviations.join(', ')}]`);
    console.log(`   Preserved: [${result.preservedAbbreviations.join(', ')}]`);
    
    if (result.missingAbbreviations.length > 0) {
      console.log(`   ⚠️  Missing: [${result.missingAbbreviations.join(', ')}]`);
    }
    
    console.log('');
  });

  if (testResults.failed > 0) {
    console.log('🔧 Some abbreviations were not preserved. Check the protection logic in textProcessor.ts');
  } else {
    console.log('🎉 All abbreviations were successfully preserved!');
  }
}

// Export for use in development/testing
export { abbreviationTests };
