'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';

/**
 * Test component to verify slider positioning
 * This component helps debug and visualize slider ball positioning
 */
export default function SliderPositionTest() {
  const [isPremium, setIsPremium] = useState(false);
  const [intensity, setIntensity] = useState<'light' | 'medium' | 'heavy'>('medium');

  // Updated logic to match ProcessingOptions component
  const getIntensityValue = () => {
    // Always use consistent visual positioning regardless of user type
    switch (intensity) {
      case 'light': return 0;    // 0% - far left
      case 'medium': return 50;  // 50% - exact center
      case 'heavy': return 100;  // 100% - far right
      default: return 50;        // default to medium
    }
  };

  const intensityValue = getIntensityValue();

  const handleIntensityChange = (value: number[]) => {
    const newValue = value[0];
    let newIntensity: 'light' | 'medium' | 'heavy';

    // Use consistent boundaries for all users based on visual position
    if (newValue <= 25) {
      newIntensity = 'light';
    } else if (newValue <= 75) {
      newIntensity = 'medium';
    } else {
      newIntensity = 'heavy';
    }

    // Restrict heavy mode to premium users
    if (newIntensity === 'heavy' && !isPremium) {
      // For non-premium users, if they try to go beyond medium, keep it at medium
      setIntensity('medium');
      return;
    }

    setIntensity(newIntensity);
  };

  const getPositionPercentage = () => {
    return intensityValue; // Since we're using 0, 50, 100 directly, this is the percentage
  };

  return (
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10 max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="text-white">Slider Position Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Type Toggle */}
        <div className="flex items-center gap-4">
          <Button
            onClick={() => setIsPremium(!isPremium)}
            variant={isPremium ? "default" : "outline"}
            className="text-white"
          >
            {isPremium ? "Premium User" : "Free User"}
          </Button>
          <Badge variant={isPremium ? "default" : "secondary"}>
            {isPremium ? "Full Access" : "Limited Access"}
          </Badge>
        </div>

        {/* Intensity Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={() => setIntensity('light')}
            variant={intensity === 'light' ? "default" : "outline"}
            size="sm"
            className="text-white"
          >
            Light
          </Button>
          <Button
            onClick={() => setIntensity('medium')}
            variant={intensity === 'medium' ? "default" : "outline"}
            size="sm"
            className="text-white"
          >
            Medium
          </Button>
          <Button
            onClick={() => setIntensity('heavy')}
            variant={intensity === 'heavy' ? "default" : "outline"}
            size="sm"
            className="text-white"
            disabled={!isPremium}
          >
            Heavy {!isPremium && "(Premium)"}
          </Button>
        </div>

        {/* Slider */}
        <div className="px-2">
          <Slider
            value={[intensityValue]}
            onValueChange={handleIntensityChange}
            max={100}
            step={1}
            className="w-full"
          />
          
          {/* Position Labels */}
          <div className="relative mt-3">
            <div className="flex justify-between items-center text-xs">
              <div className="flex flex-col items-center">
                <span className={`font-medium ${intensity === 'light' ? 'text-blue-400' : 'text-white'}`}>
                  Light
                </span>
                <span className="text-gray-400 text-xs">0%</span>
              </div>
              
              <div className="flex flex-col items-center absolute left-1/2 transform -translate-x-1/2">
                <span className={`font-medium ${intensity === 'medium' ? 'text-purple-400' : 'text-white'}`}>
                  Medium
                </span>
                <span className="text-gray-400 text-xs">50%</span>
              </div>
              
              <div className="flex flex-col items-center">
                <span className={`font-medium ${
                  !isPremium ? 'text-gray-500' : intensity === 'heavy' ? 'text-yellow-400' : 'text-white'
                }`}>
                  Heavy
                </span>
                <span className="text-gray-400 text-xs">100%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Debug Information */}
        <div className="bg-gray-800/50 p-4 rounded-lg text-sm">
          <h4 className="text-white font-medium mb-2">Debug Information:</h4>
          <div className="space-y-1 text-gray-300">
            <div>Current Intensity: <span className="text-blue-400">{intensity}</span></div>
            <div>Slider Value: <span className="text-green-400">{intensityValue}</span></div>
            <div>Slider Max: <span className="text-yellow-400">100</span></div>
            <div>Position: <span className="text-purple-400">{getPositionPercentage().toFixed(1)}%</span></div>
            <div>User Type: <span className="text-orange-400">{isPremium ? 'Premium' : 'Free'}</span></div>
          </div>

          <div className="mt-3 text-xs text-gray-400">
            <div>Expected Positions: Light=0%, Medium=50%, Heavy=100%</div>
            <div>Current: {intensity === 'light' ? `✅ Light at ${getPositionPercentage()}%` :
                         intensity === 'medium' ? `✅ Medium at ${getPositionPercentage()}%` :
                         intensity === 'heavy' ? `✅ Heavy at ${getPositionPercentage()}%` : 'Unknown'}</div>
            <div className="mt-1">
              {intensity === 'light' && getPositionPercentage() === 0 && <span className="text-green-400">✅ Perfect positioning!</span>}
              {intensity === 'medium' && getPositionPercentage() === 50 && <span className="text-green-400">✅ Perfect positioning!</span>}
              {intensity === 'heavy' && getPositionPercentage() === 100 && <span className="text-green-400">✅ Perfect positioning!</span>}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
