/**
 * Test suite for text processor abbreviation handling
 * This file helps verify that abbreviations are properly protected during humanization
 */

import { processTextDirectly } from './textProcessor';

interface TestCase {
  name: string;
  input: string;
  expectedAbbreviations: string[];
  description: string;
}

const testCases: TestCase[] = [
  {
    name: 'Basic AI and LLM abbreviations',
    input: 'AI and LLM are transforming the tech industry. API integration is crucial.',
    expectedAbbreviations: ['AI', 'LLM', 'API'],
    description: 'Common tech abbreviations should remain unchanged'
  },
  {
    name: 'Mixed case with abbreviations',
    input: 'The CEO announced that our AI-powered API will integrate with HTML and CSS frameworks.',
    expectedAbbreviations: ['CEO', 'AI', 'API', 'HTML', 'CSS'],
    description: 'Abbreviations in mixed context should be preserved'
  },
  {
    name: 'Technical abbreviations',
    input: 'Our SQL database connects to REST APIs using JSON over HTTPS protocols.',
    expectedAbbreviations: ['SQL', 'REST', 'APIs', 'JSON', 'HTTPS'],
    description: 'Technical abbreviations should remain intact'
  },
  {
    name: 'Business abbreviations',
    input: 'The CFO and CTO discussed ROI metrics for our B2B SaaS platform.',
    expectedAbbreviations: ['CFO', 'CTO', 'ROI', 'B2B', 'SaaS'],
    description: 'Business abbreviations should be protected'
  },
  {
    name: 'Educational abbreviations',
    input: 'She has a PhD in Computer Science and an MBA from a top university.',
    expectedAbbreviations: ['PhD', 'MBA'],
    description: 'Educational abbreviations should remain unchanged'
  },
  {
    name: 'Geographic and organizational',
    input: 'The USA, UK, and EU are working with NATO and the UN on this initiative.',
    expectedAbbreviations: ['USA', 'UK', 'EU', 'NATO', 'UN'],
    description: 'Geographic and organizational abbreviations should be preserved'
  },
  {
    name: 'File extensions and domains',
    input: 'Upload your .pdf files to example.com or send them via email.',
    expectedAbbreviations: ['.pdf', 'example.com'],
    description: 'File extensions and domain names should be protected'
  },
  {
    name: 'Version numbers and technical terms',
    input: 'We upgraded to React v18.2 and Next.js for better performance.',
    expectedAbbreviations: ['React', 'v18.2', 'Next.js'],
    description: 'Version numbers and technical terms should remain intact'
  }
];

/**
 * Run a single test case and return the results
 */
function runTestCase(testCase: TestCase): {
  passed: boolean;
  result: string;
  missingAbbreviations: string[];
  unexpectedChanges: string[];
} {
  console.log(`\n🧪 Testing: ${testCase.name}`);
  console.log(`Input: "${testCase.input}"`);
  
  const result = processTextDirectly(testCase.input, {
    intensity: 'medium',
    style: 'balanced',
    language: 'en'
  });
  
  console.log(`Output: "${result.humanizedText}"`);
  
  const missingAbbreviations: string[] = [];
  const unexpectedChanges: string[] = [];
  
  // Check if all expected abbreviations are present in the output
  testCase.expectedAbbreviations.forEach(abbrev => {
    if (!result.humanizedText.includes(abbrev)) {
      missingAbbreviations.push(abbrev);
    }
  });
  
  // Check for any placeholder artifacts that weren't replaced
  const placeholderPattern = /__[A-Z_]+\d+__/g;
  const remainingPlaceholders = result.humanizedText.match(placeholderPattern);
  if (remainingPlaceholders) {
    unexpectedChanges.push(...remainingPlaceholders);
  }
  
  const passed = missingAbbreviations.length === 0 && unexpectedChanges.length === 0;
  
  if (passed) {
    console.log('✅ PASSED');
  } else {
    console.log('❌ FAILED');
    if (missingAbbreviations.length > 0) {
      console.log(`   Missing abbreviations: ${missingAbbreviations.join(', ')}`);
    }
    if (unexpectedChanges.length > 0) {
      console.log(`   Unexpected changes: ${unexpectedChanges.join(', ')}`);
    }
  }
  
  return {
    passed,
    result: result.humanizedText,
    missingAbbreviations,
    unexpectedChanges
  };
}

/**
 * Run all test cases and return a summary
 */
export function runAbbreviationTests(): {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: Array<{
    testCase: TestCase;
    passed: boolean;
    result: string;
    missingAbbreviations: string[];
    unexpectedChanges: string[];
  }>;
} {
  console.log('🚀 Starting Abbreviation Protection Tests\n');
  console.log('=' .repeat(60));
  
  const results = testCases.map(testCase => ({
    testCase,
    ...runTestCase(testCase)
  }));
  
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = results.length - passedTests;
  
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST SUMMARY');
  console.log(`Total Tests: ${results.length}`);
  console.log(`Passed: ${passedTests} ✅`);
  console.log(`Failed: ${failedTests} ❌`);
  console.log(`Success Rate: ${Math.round((passedTests / results.length) * 100)}%`);
  
  if (failedTests > 0) {
    console.log('\n🔍 FAILED TESTS:');
    results.filter(r => !r.passed).forEach(r => {
      console.log(`- ${r.testCase.name}`);
      if (r.missingAbbreviations.length > 0) {
        console.log(`  Missing: ${r.missingAbbreviations.join(', ')}`);
      }
      if (r.unexpectedChanges.length > 0) {
        console.log(`  Issues: ${r.unexpectedChanges.join(', ')}`);
      }
    });
  }
  
  return {
    totalTests: results.length,
    passedTests,
    failedTests,
    results
  };
}

/**
 * Quick test function for debugging specific abbreviations
 */
export function testSpecificAbbreviation(text: string, expectedAbbrevs: string[]): void {
  console.log(`\n🔍 Quick Test: "${text}"`);
  
  const result = processTextDirectly(text, {
    intensity: 'medium',
    style: 'balanced',
    language: 'en'
  });
  
  console.log(`Result: "${result.humanizedText}"`);
  
  expectedAbbrevs.forEach(abbrev => {
    const found = result.humanizedText.includes(abbrev);
    console.log(`${found ? '✅' : '❌'} ${abbrev}: ${found ? 'PRESERVED' : 'MISSING'}`);
  });
  
  // Check for placeholders
  const placeholders = result.humanizedText.match(/__[A-Z_]+\d+__/g);
  if (placeholders) {
    console.log(`⚠️  Unreplaced placeholders: ${placeholders.join(', ')}`);
  }
}

// Example usage:
// import { runAbbreviationTests, testSpecificAbbreviation } from './textProcessorTest';
// 
// // Run all tests
// runAbbreviationTests();
// 
// // Test specific case
// testSpecificAbbreviation('AI and LLM are powerful technologies', ['AI', 'LLM']);
