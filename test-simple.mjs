/**
 * Simple test to verify abbreviation preservation
 * Run with: node test-simple.mjs
 */

// Test the protection function directly
function testProtectCapitalizationAndTerms() {
  console.log('🧪 Testing protectCapitalizationAndTerms function\n');
  
  // Simulate the function logic
  function protectCapitalizationAndTerms(text, protectedTerms = []) {
    let result = text;
    const protectionMap = new Map();

    // Critical abbreviations that must always be protected (expanded list)
    const criticalAbbreviations = [
      'AI', 'API', 'UI', 'UX', 'IT', 'HR', 'PR', 'SEO', 'CEO', 'CTO', 'CFO',
      'LLM', 'HTML', 'CSS', 'XML', 'JSON', 'HTTP', 'HTTPS', 'URL', 'URI',
      'SQL', 'NoSQL', 'REST', 'SOAP', 'JWT', 'OAuth', 'SSL', 'TLS',
      'AWS', 'GCP', 'IBM', 'NASA', 'FBI', 'CIA', 'USA', 'UK', 'EU',
      'PDF', 'CSV', 'ZIP', 'PNG', 'JPG', 'JPEG', 'GIF', 'SVG',
      'iOS', 'macOS', 'Windows', 'Linux', 'Android', 'GNU', 'MIT',
      'GPS', 'WiFi', 'Bluetooth', 'NFC', 'RFID', 'IoT', 'AR', 'VR'
    ];

    // Find and protect all-caps abbreviations (1+ consecutive capitals)
    const allCapsPattern = /\b[A-Z]{1,}\b/g;
    const allCapsMatches = text.match(allCapsPattern) || [];
    const uniqueAllCaps = Array.from(new Set(allCapsMatches.filter(term => 
      // Only keep terms that are purely uppercase letters (no numbers or special chars)
      /^[A-Z]+$/.test(term) && term.length >= 1
    )));

    // Find critical abbreviations that exist in the text
    const foundCritical = criticalAbbreviations.filter(term =>
      new RegExp(`\\b${term}\\b`, 'g').test(text)
    );

    // Combine all terms to protect, prioritizing critical terms first
    const allTerms = [
      ...foundCritical,           // Critical abbreviations first
      ...uniqueAllCaps,           // All-caps terms
      ...protectedTerms,          // User-provided protected terms
    ];

    // Remove duplicates while preserving order (most important terms first)
    const allProtectedTerms = Array.from(new Set(allTerms.filter(term => term && term.length > 0)));

    console.log('Found terms to protect:', allProtectedTerms);

    // Create protection placeholders
    allProtectedTerms.forEach((term, index) => {
      const placeholder = `__PROTECTED_TERM_${index}__`;
      const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(`\\b${escapedTerm}\\b`, 'g');

      if (regex.test(result)) {
        result = result.replace(regex, placeholder);
        protectionMap.set(placeholder, term);
        console.log(`Protected: ${term} -> ${placeholder}`);
      }
    });

    return { protectedResult: result, protectionMap };
  }

  // Test cases
  const testCases = [
    'AI and LLM are transforming the tech industry. API integration is crucial.',
    'The CEO announced that our CTO will lead the IT initiative with HR support.',
    'HTML and CSS files need optimization. JSON and XML parsing is important.',
    'JavaScript uses API calls to process HTML. The UI/UX design needs CSS.',
  ];

  testCases.forEach((testCase, index) => {
    console.log(`\n📝 Test ${index + 1}: "${testCase}"`);
    
    const { protectedResult, protectionMap } = protectCapitalizationAndTerms(testCase);
    
    console.log(`Protected text: "${protectedResult}"`);
    console.log('Protection map:', Array.from(protectionMap.entries()));
    
    // Simulate restoration
    let restored = protectedResult;
    const sortedEntries = Array.from(protectionMap.entries()).reverse();
    sortedEntries.forEach(([placeholder, original]) => {
      const escapedPlaceholder = placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
      const regex = new RegExp(escapedPlaceholder, 'g');
      
      if (regex.test(restored)) {
        restored = restored.replace(regex, original);
        console.log(`Restored: ${placeholder} -> ${original}`);
      }
    });
    
    console.log(`Final restored text: "${restored}"`);
    
    // Check for unreplaced placeholders
    const remainingPlaceholders = restored.match(/__[A-Z_]+\d+__/g);
    if (remainingPlaceholders) {
      console.log('⚠️  Unreplaced placeholders:', remainingPlaceholders);
    } else {
      console.log('✅ No unreplaced placeholders');
    }
    
    console.log('---');
  });
}

testProtectCapitalizationAndTerms();
