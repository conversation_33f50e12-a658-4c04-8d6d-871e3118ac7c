'use client';

import { useEffect, useState } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Gift, Users, Zap, ArrowRight, CheckCircle, AlertCircle } from 'lucide-react';

/**
 * Referral Landing Page
 * Handles referral link processing and user onboarding
 */
export default function ReferralPage() {
  const params = useParams();
  const router = useRouter();
  const { data: session, status } = useSession();
  const [referralCode, setReferralCode] = useState<string>('');
  const [validating, setValidating] = useState(true);
  const [isValid, setIsValid] = useState(false);
  const [processing, setProcessing] = useState(false);
  const [message, setMessage] = useState('');
  const [bonusCredits, setBonusCredits] = useState(0);

  useEffect(() => {
    if (params?.code) {
      const code = Array.isArray(params.code) ? params.code[0] : params.code;
      setReferralCode(code);
      validateReferralCode(code);
    }
  }, [params]);

  useEffect(() => {
    // If user is signed in and we have a valid referral code, process it
    if (session?.user && isValid && referralCode && !processing) {
      processReferral();
    }
  }, [session, isValid, referralCode]);

  const validateReferralCode = async (code: string) => {
    try {
      setValidating(true);
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'validate_code',
          referralCode: code
        }),
      });

      const data = await response.json();
      
      if (data.success && data.data.valid) {
        setIsValid(true);
        setBonusCredits(data.data.bonusCredits);
        setMessage('Valid referral code! Sign in to claim your bonus credits.');
      } else {
        setIsValid(false);
        setMessage(data.data?.message || 'Invalid referral code');
      }
    } catch (error) {
      console.error('Error validating referral code:', error);
      setIsValid(false);
      setMessage('Error validating referral code');
    } finally {
      setValidating(false);
    }
  };

  const processReferral = async () => {
    try {
      setProcessing(true);
      const response = await fetch('/api/referrals', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'track_referral',
          referralCode: referralCode
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        setMessage(`🎉 ${data.data.message}`);
        // Redirect to main app after 3 seconds
        setTimeout(() => {
          router.push('/');
        }, 3000);
      } else {
        setMessage(data.error || 'Failed to process referral');
      }
    } catch (error) {
      console.error('Error processing referral:', error);
      setMessage('Error processing referral');
    } finally {
      setProcessing(false);
    }
  };

  const handleSignIn = () => {
    // Store referral code in localStorage to process after sign-in
    localStorage.setItem('pendingReferralCode', referralCode);
    router.push('/auth/signin');
  };

  if (validating) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
        <Card className="bg-white/5 backdrop-blur-lg border-white/10 max-w-md w-full">
          <CardContent className="p-6 text-center">
            <div className="animate-spin w-8 h-8 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="text-white">Validating referral code...</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <Gift className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-2xl font-bold text-white mb-2">Welcome to GhostLayer!</h1>
          <p className="text-gray-300">You've been invited to join the best AI text humanizer</p>
        </div>

        {/* Referral Status Card */}
        <Card className="bg-white/5 backdrop-blur-lg border-white/10">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              {isValid ? (
                <>
                  <CheckCircle className="w-5 h-5 text-green-400" />
                  Referral Bonus Available!
                </>
              ) : (
                <>
                  <AlertCircle className="w-5 h-5 text-red-400" />
                  Invalid Referral Code
                </>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isValid ? (
              <>
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-2">
                    <Zap className="w-4 h-4 text-green-400" />
                    <span className="text-green-300 font-medium">Bonus Credits</span>
                  </div>
                  <div className="text-2xl font-bold text-green-400">+{bonusCredits} Credits</div>
                  <p className="text-green-200 text-sm mt-1">
                    Free credits to get you started with premium features!
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="text-white font-medium">What you get:</h4>
                  <ul className="text-gray-300 text-sm space-y-1">
                    <li>• {bonusCredits} bonus credits (worth ${(bonusCredits * 0.01).toFixed(2)})</li>
                    <li>• Access to all humanization modes</li>
                    <li>• Premium text processing features</li>
                    <li>• Priority customer support</li>
                  </ul>
                </div>

                {status === 'loading' ? (
                  <div className="text-center py-4">
                    <div className="animate-spin w-6 h-6 border-2 border-blue-400 border-t-transparent rounded-full mx-auto mb-2"></div>
                    <p className="text-gray-300 text-sm">Checking authentication...</p>
                  </div>
                ) : session?.user ? (
                  <div className="text-center">
                    {processing ? (
                      <div className="py-4">
                        <div className="animate-spin w-6 h-6 border-2 border-green-400 border-t-transparent rounded-full mx-auto mb-2"></div>
                        <p className="text-green-300 text-sm">Processing your bonus...</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Badge className="bg-green-600 text-white">
                          Signed in as {session.user.name || session.user.email}
                        </Badge>
                        <p className="text-green-300 text-sm">{message}</p>
                        {message.includes('🎉') && (
                          <p className="text-gray-400 text-xs">Redirecting to app in 3 seconds...</p>
                        )}
                      </div>
                    )}
                  </div>
                ) : (
                  <Button
                    onClick={handleSignIn}
                    className="w-full bg-green-600 hover:bg-green-700"
                  >
                    <Users className="w-4 h-4 mr-2" />
                    Sign In to Claim Bonus
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </>
            ) : (
              <div className="text-center">
                <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-4">
                  <p className="text-red-300">{message}</p>
                </div>
                <Button
                  onClick={() => router.push('/')}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  Go to GhostLayer
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Features Preview */}
        {isValid && (
          <Card className="bg-white/5 backdrop-blur-lg border-white/10">
            <CardHeader>
              <CardTitle className="text-white text-sm">Why GhostLayer?</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Zap className="w-4 h-4 text-blue-400" />
                  </div>
                  <p className="text-white font-medium">AI-Powered</p>
                  <p className="text-gray-400 text-xs">Advanced humanization</p>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-2">
                    <Users className="w-4 h-4 text-purple-400" />
                  </div>
                  <p className="text-white font-medium">Trusted</p>
                  <p className="text-gray-400 text-xs">10,000+ users</p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
