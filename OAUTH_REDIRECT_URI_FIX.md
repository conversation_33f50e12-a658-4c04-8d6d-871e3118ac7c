# 🔧 Google OAuth redirect_uri_mismatch Error Fix

## Problem Diagnosis
You're getting a `redirect_uri_mismatch` error because the redirect URI configured in Google Cloud Console doesn't exactly match what NextAuth.js is sending.

## Current Configuration Analysis
- **NEXTAUTH_URL**: `http://localhost:3000`
- **Google Client ID**: `637401884201-7jro0haaqqs8acdkvd3rac7mopojca36.apps.googleusercontent.com`
- **Google Client Secret**: ✅ Properly configured
- **Expected Callback URL**: `http://localhost:3000/api/auth/callback/google`

## 🎯 Step-by-Step Fix

### Step 1: Verify Current Google Cloud Console Settings
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Navigate to **"APIs & Services"** → **"Credentials"**
3. Find your OAuth 2.0 Client ID: `637401884201-7jro0haaqqs8acdkvd3rac7mopojca36.apps.googleusercontent.com`
4. Click on it to edit

### Step 2: Update Authorized Redirect URIs
In the OAuth client configuration, ensure you have **EXACTLY** these URIs:

**Authorized JavaScript origins:**
```
http://localhost:3000
```

**Authorized redirect URIs:**
```
http://localhost:3000/api/auth/callback/google
```

⚠️ **CRITICAL**: Make sure there are:
- No trailing slashes
- No extra spaces
- Exact case matching
- No HTTPS for localhost (use HTTP)

### Step 3: Common Mistakes to Avoid
❌ **Wrong**: `http://localhost:3000/api/auth/callback/google/`
❌ **Wrong**: `https://localhost:3000/api/auth/callback/google`
❌ **Wrong**: `http://localhost:3000/api/auth/callback/Google`
✅ **Correct**: `http://localhost:3000/api/auth/callback/google`

### Step 4: Save and Wait
1. Click **"Save"** in Google Cloud Console
2. Wait 5-10 minutes for changes to propagate
3. Clear your browser cache and cookies for localhost:3000

### Step 5: Test the Fix
1. Restart your development server:
   ```bash
   npm run dev
   ```
2. Try signing in with Google again
3. Check browser developer tools for any error messages

## 🔍 Debug Information

### NextAuth.js Callback URL Format
NextAuth.js automatically generates the callback URL as:
```
{NEXTAUTH_URL}/api/auth/callback/{provider}
```

For Google provider:
```
http://localhost:3000/api/auth/callback/google
```

### Verification Steps
1. **Check Network Tab**: In browser dev tools, look for the OAuth request and verify the `redirect_uri` parameter
2. **Check Server Logs**: Look for NextAuth debug logs if `NEXTAUTH_DEBUG=true`
3. **Test with Incognito**: Use a private/incognito browser window

## 🚨 If Still Not Working

### Enable Debug Mode
Add to your `.env.local`:
```env
NEXTAUTH_DEBUG=true
```

### Check for Multiple OAuth Clients
- Ensure you're editing the correct OAuth client in Google Cloud Console
- Verify the Client ID matches exactly

### Verify OAuth Consent Screen
1. Go to **"OAuth consent screen"**
2. Ensure status is not "Needs verification"
3. Add your email as a test user if in development mode

### Alternative Debugging
Create a test endpoint to verify the exact callback URL:

```typescript
// pages/api/debug-oauth.ts
export default function handler(req, res) {
  const callbackUrl = `${process.env.NEXTAUTH_URL}/api/auth/callback/google`;
  res.json({
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    expectedCallback: callbackUrl,
    clientId: process.env.GOOGLE_CLIENT_ID
  });
}
```

## 📋 Final Checklist
- [ ] Authorized redirect URI is exactly: `http://localhost:3000/api/auth/callback/google`
- [ ] No trailing slashes or extra characters
- [ ] Changes saved in Google Cloud Console
- [ ] Waited 5-10 minutes for propagation
- [ ] Cleared browser cache
- [ ] Restarted development server
- [ ] Tested in incognito/private window

## 🎉 Success Indicators
When fixed, you should:
1. Be redirected to Google's OAuth consent screen
2. See your app name "GhostLayer" 
3. Be able to grant permissions
4. Be redirected back to your app successfully
5. See your user session created

## Production Notes
For production deployment, you'll need to:
1. Add your production domain to authorized origins
2. Add production callback URL: `https://yourdomain.com/api/auth/callback/google`
3. Update `NEXTAUTH_URL` environment variable for production
