/**
 * Test script to verify batch processing functionality
 * Run with: node test-batch-processing.js
 */

// Create test files in memory
function createTestFile(name, content, type = 'text/plain') {
  // Simulate File object
  const file = {
    name: name,
    size: content.length,
    type: type,
    lastModified: Date.now(),
    // Simulate file reading methods
    text: () => Promise.resolve(content),
    arrayBuffer: () => Promise.resolve(new TextEncoder().encode(content).buffer)
  };
  return file;
}

// Test file validation
function testFileValidation() {
  console.log('🧪 Testing File Validation\n');
  
  // Simulate the validation function
  function validateFile(file) {
    // Check file size (max 50MB)
    if (file.size > 50 * 1024 * 1024) {
      return { valid: false, error: 'File size exceeds 50MB limit' };
    }
    
    // Check if file type is supported
    const fileType = file.type;
    const fileName = file.name.toLowerCase();
    
    const supportedExtensions = ['.txt', '.md', '.docx', '.pdf', '.rtf', '.odt', '.json', '.csv', '.html', '.htm', '.xml'];
    const supportedTypes = ['text/plain', 'text/markdown', 'application/json', 'text/html', 'text/xml'];
    
    const isSupportedType = supportedTypes.includes(fileType) ||
      supportedExtensions.some(ext => fileName.endsWith(ext));
    
    if (!isSupportedType) {
      return { valid: false, error: `Unsupported file type. Supported formats: ${supportedExtensions.join(', ')}` };
    }
    
    return { valid: true };
  }

  const testFiles = [
    createTestFile('test.txt', 'This is a test text file.', 'text/plain'),
    createTestFile('test.md', '# Markdown Test\nThis is markdown content.', 'text/markdown'),
    createTestFile('test.json', '{"message": "Hello World"}', 'application/json'),
    createTestFile('test.html', '<html><body>Hello HTML</body></html>', 'text/html'),
    createTestFile('test.unsupported', 'Unsupported content', 'application/unknown'),
    createTestFile('large-file.txt', 'x'.repeat(60 * 1024 * 1024), 'text/plain'), // 60MB file
  ];

  testFiles.forEach((file, index) => {
    const validation = validateFile(file);
    console.log(`📄 File ${index + 1}: ${file.name}`);
    console.log(`   Type: ${file.type}`);
    console.log(`   Size: ${(file.size / 1024).toFixed(2)} KB`);
    console.log(`   Valid: ${validation.valid ? '✅' : '❌'}`);
    if (!validation.valid) {
      console.log(`   Error: ${validation.error}`);
    }
    console.log('');
  });
}

// Test text extraction
async function testTextExtraction() {
  console.log('🧪 Testing Text Extraction\n');
  
  // Simulate text extraction functions
  async function readTextFile(file) {
    return await file.text();
  }

  async function extractFromJson(file) {
    try {
      const content = await readTextFile(file);
      const jsonData = JSON.parse(content);
      
      // Convert JSON to readable text
      function jsonToText(obj, indent = 0) {
        const spaces = '  '.repeat(indent);
        let result = '';
        
        if (typeof obj === 'object' && obj !== null) {
          if (Array.isArray(obj)) {
            obj.forEach((item, index) => {
              result += `${spaces}Item ${index + 1}: ${jsonToText(item, indent + 1)}\n`;
            });
          } else {
            Object.entries(obj).forEach(([key, value]) => {
              result += `${spaces}${key}: ${jsonToText(value, indent + 1)}\n`;
            });
          }
        } else {
          result = String(obj);
        }
        
        return result;
      }
      
      return jsonToText(jsonData);
    } catch (error) {
      throw new Error(`Failed to process JSON file: ${error.message}`);
    }
  }

  async function extractFromHtml(file) {
    try {
      const content = await readTextFile(file);
      if (!content || content.trim().length === 0) {
        throw new Error('HTML file appears to be empty');
      }

      // Simple HTML text extraction (remove tags)
      let text = content.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, ''); // Remove scripts
      text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, ''); // Remove styles
      text = text.replace(/<[^>]*>/g, ' '); // Remove all HTML tags
      text = text.replace(/\s+/g, ' ').trim(); // Clean up whitespace

      return text;
    } catch (error) {
      throw new Error(`Failed to process HTML file: ${error.message}`);
    }
  }

  async function extractTextFromFile(file) {
    const fileType = file.type;
    const fileName = file.name.toLowerCase();

    try {
      switch (fileType) {
        case 'text/plain':
        case 'text/markdown':
          return await readTextFile(file);
        
        case 'application/json':
          return await extractFromJson(file);
        
        case 'text/html':
          return await extractFromHtml(file);
        
        default:
          // Try to detect by file extension
          if (fileName.endsWith('.txt') || fileName.endsWith('.md')) {
            return await readTextFile(file);
          } else if (fileName.endsWith('.json')) {
            return await extractFromJson(file);
          } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
            return await extractFromHtml(file);
          } else {
            // Try to read as plain text as last resort
            const content = await readTextFile(file);
            if (content && content.trim().length > 0) {
              return content;
            }
            throw new Error(`Unsupported file type: ${fileType || 'unknown'}`);
          }
      }
    } catch (error) {
      throw new Error(`Failed to extract text from ${file.name}: ${error.message}`);
    }
  }

  const testFiles = [
    createTestFile('test.txt', 'This is a plain text file with some content to humanize.', 'text/plain'),
    createTestFile('test.md', '# Markdown Test\n\nThis is **markdown** content with *formatting*.', 'text/markdown'),
    createTestFile('test.json', '{"title": "Test Document", "content": "This is JSON content", "author": "Test User"}', 'application/json'),
    createTestFile('test.html', '<html><head><title>Test</title></head><body><h1>Hello World</h1><p>This is HTML content.</p></body></html>', 'text/html'),
  ];

  for (const file of testFiles) {
    try {
      console.log(`📄 Extracting from: ${file.name}`);
      const extractedText = await extractTextFromFile(file);
      console.log(`   Original size: ${file.size} bytes`);
      console.log(`   Extracted length: ${extractedText.length} characters`);
      console.log(`   Preview: ${extractedText.substring(0, 100)}${extractedText.length > 100 ? '...' : ''}`);
      console.log('   ✅ Success\n');
    } catch (error) {
      console.log(`   ❌ Error: ${error.message}\n`);
    }
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Batch Processing Test Suite\n');
  console.log('================================\n');
  
  testFileValidation();
  console.log('================================\n');
  await testTextExtraction();
  
  console.log('✅ All tests completed!');
}

runTests().catch(console.error);
