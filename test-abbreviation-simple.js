/**
 * Simple test to verify abbreviation preservation
 */

// Test the protection function directly
const testInput = "AI and LLM are powerful technologies. API integration is crucial.";
console.log('Testing abbreviation preservation...');
console.log('Input:', testInput);

// Test the protection function
function testProtection() {
  // Simulate the protection logic
  const criticalAbbreviations = [
    'AI', 'API', 'UI', 'UX', 'IT', 'HR', 'PR', 'SEO', 'CEO', 'CTO', 'CFO',
    'LLM', 'HTML', 'CSS', 'XML', 'JSON', 'HTTP', 'HTTPS', 'URL', 'URI'
  ];
  
  let result = testInput;
  const protectionMap = new Map();
  
  // Find critical abbreviations that exist in the text
  const foundCritical = criticalAbbreviations.filter(term =>
    new RegExp(`\\b${term}\\b`, 'g').test(testInput)
  );
  
  console.log('Found critical abbreviations:', foundCritical);
  
  // Create protection placeholders
  foundCritical.forEach((term, index) => {
    const placeholder = `__PROTECTED_TERM_${index}__`;
    const escapedTerm = term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(`\\b${escapedTerm}\\b`, 'g');
    
    if (regex.test(result)) {
      result = result.replace(regex, placeholder);
      protectionMap.set(placeholder, term);
      console.log(`Protected: ${term} -> ${placeholder}`);
    }
  });
  
  console.log('Protected text:', result);
  console.log('Protection map:', Array.from(protectionMap.entries()));
  
  // Test restoration
  const sortedEntries = Array.from(protectionMap.entries()).reverse();
  sortedEntries.forEach(([placeholder, original]) => {
    const escapedPlaceholder = placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    const regex = new RegExp(escapedPlaceholder, 'g');
    
    if (regex.test(result)) {
      result = result.replace(regex, original);
      console.log(`Restored: ${placeholder} -> ${original}`);
    }
  });
  
  console.log('Final result:', result);
  
  // Check if abbreviations are preserved
  const preservedAI = result.includes('AI');
  const preservedLLM = result.includes('LLM');
  const preservedAPI = result.includes('API');
  
  console.log('Preservation check:');
  console.log('- AI preserved:', preservedAI);
  console.log('- LLM preserved:', preservedLLM);
  console.log('- API preserved:', preservedAPI);
  
  const allPreserved = preservedAI && preservedLLM && preservedAPI;
  console.log('All abbreviations preserved:', allPreserved);
  
  return allPreserved;
}

testProtection();
