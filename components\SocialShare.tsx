'use client';

import { useState, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Share2,
  Twitter,
  Linkedin,
  Facebook,
  Instagram,
  Copy,
  Download,
  Sparkles,
  TrendingUp,
  Users,
  CheckCircle,
  Gift,
  Crown,
  Coins
} from 'lucide-react';
import { ProcessingResult } from '@/types';
import { analytics } from '@/lib/analytics';
import { shareRewardManager, ShareReward } from '@/lib/shareRewards';

interface SocialShareProps {
  originalText: string;
  result: ProcessingResult;
  onShare?: (platform: string) => void;
}

export default function SocialShare({ originalText, result, onShare }: SocialShareProps) {
  const { data: session } = useSession();
  const [isGeneratingImage, setIsGeneratingImage] = useState(false);
  const [copiedLink, setCopiedLink] = useState(false);
  const [earnedRewards, setEarnedRewards] = useState<ShareReward[]>([]);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [incentiveMessage, setIncentiveMessage] = useState('');
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Load share incentive message
  useEffect(() => {
    if (session?.user?.id) {
      const message = shareRewardManager.getShareIncentiveMessage(session.user.id);
      setIncentiveMessage(message);
    }
  }, [session]);

  // Generate shareable image with before/after comparison
  const generateShareableImage = async (): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = canvasRef.current;
      if (!canvas) return resolve('');

      const ctx = canvas.getContext('2d');
      if (!ctx) return resolve('');

      // Set canvas size
      canvas.width = 1200;
      canvas.height = 630; // Optimal for social media

      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
      gradient.addColorStop(0, '#0f172a'); // slate-900
      gradient.addColorStop(0.5, '#1e3a8a'); // blue-800
      gradient.addColorStop(1, '#0f172a'); // slate-900
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // GhostLayer branding
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 32px Arial';
      ctx.fillText('GhostLayer', 50, 60);
      
      ctx.fillStyle = '#94a3b8';
      ctx.font = '18px Arial';
      ctx.fillText('AI Text Humanization', 50, 90);

      // Improvement badge
      ctx.fillStyle = '#10b981';
      ctx.fillRect(50, 110, 200, 40);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.fillText(`${result.improvementScore}% More Human`, 60, 135);

      // Before section
      ctx.fillStyle = '#ef4444';
      ctx.fillRect(50, 180, 500, 30);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.fillText('BEFORE (AI-Generated)', 60, 200);

      // Original text (truncated)
      ctx.fillStyle = '#e2e8f0';
      ctx.font = '14px Arial';
      const originalLines = wrapText(ctx, originalText, 480, 5);
      originalLines.forEach((line, index) => {
        ctx.fillText(line, 60, 230 + (index * 20));
      });

      // After section
      ctx.fillStyle = '#10b981';
      ctx.fillRect(650, 180, 500, 30);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 16px Arial';
      ctx.fillText('AFTER (Humanized)', 660, 200);

      // Humanized text (truncated)
      ctx.fillStyle = '#e2e8f0';
      ctx.font = '14px Arial';
      const humanizedLines = wrapText(ctx, result.humanizedText, 480, 5);
      humanizedLines.forEach((line, index) => {
        ctx.fillText(line, 660, 230 + (index * 20));
      });

      // Call to action
      ctx.fillStyle = '#6366f1';
      ctx.fillRect(400, 520, 400, 60);
      ctx.fillStyle = '#ffffff';
      ctx.font = 'bold 20px Arial';
      ctx.fillText('Try GhostLayer Free →', 450, 555);

      // Convert to data URL
      resolve(canvas.toDataURL('image/png'));
    });
  };

  // Helper function to wrap text
  const wrapText = (ctx: CanvasRenderingContext2D, text: string, maxWidth: number, maxLines: number): string[] => {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine + (currentLine ? ' ' : '') + word;
      const metrics = ctx.measureText(testLine);
      
      if (metrics.width > maxWidth && currentLine) {
        lines.push(currentLine);
        currentLine = word;
        
        if (lines.length >= maxLines - 1) {
          lines.push(currentLine + '...');
          break;
        }
      } else {
        currentLine = testLine;
      }
    }
    
    if (currentLine && lines.length < maxLines) {
      lines.push(currentLine);
    }
    
    return lines;
  };

  // Share to specific platform
  const shareToSocial = async (platform: string) => {
    setIsGeneratingImage(true);

    try {
      const shareText = generateShareText();
      const shareUrl = window.location.href;

      let url = '';

      switch (platform) {
        case 'twitter':
          url = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`;
          break;
        case 'linkedin':
          url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}&summary=${encodeURIComponent(shareText)}`;
          break;
        case 'facebook':
          url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}&quote=${encodeURIComponent(shareText)}`;
          break;
      }

      if (url) {
        window.open(url, '_blank', 'width=600,height=400');

        // Process share rewards
        if (session?.user?.id) {
          const rewards = await shareRewardManager.processShare(session.user.id, platform, {
            shareText,
            shareUrl,
            improvementScore: result.improvementScore
          });

          if (rewards.length > 0) {
            setEarnedRewards(rewards);
            setShowRewardModal(true);

            // Update incentive message
            const newMessage = shareRewardManager.getShareIncentiveMessage(session.user.id);
            setIncentiveMessage(newMessage);
          }
        }

        analytics.trackShare(platform);
        onShare?.(platform);
      }
    } finally {
      setIsGeneratingImage(false);
    }
  };

  // Generate compelling share text
  const generateShareText = (): string => {
    const templates = [
      `🤖➡️👤 Just transformed AI text into human-like writing with ${result.improvementScore}% improvement using @GhostLayer! The difference is incredible. #AIHumanization #ContentCreation`,
      `✨ Wow! GhostLayer just made my AI-generated content ${result.improvementScore}% more human and natural. This tool is a game-changer! #GhostLayer #AIWriting`,
      `🚀 From robotic to natural in seconds! GhostLayer improved my text by ${result.improvementScore}%. Perfect for content creators! #AITools #Writing`,
      `💡 Mind blown! GhostLayer transformed my AI text with ${result.improvementScore}% improvement. The before/after is amazing! #AIHumanization #TechTools`
    ];
    
    return templates[Math.floor(Math.random() * templates.length)];
  };

  // Copy shareable link
  const copyShareableLink = async () => {
    const shareText = generateShareText();
    const shareUrl = window.location.href;
    const fullText = `${shareText}\n\n${shareUrl}`;
    
    await navigator.clipboard.writeText(fullText);
    setCopiedLink(true);
    setTimeout(() => setCopiedLink(false), 2000);
    
    analytics.trackShare('copy');
  };

  // Download shareable image
  const downloadShareableImage = async () => {
    setIsGeneratingImage(true);
    
    try {
      const imageDataUrl = await generateShareableImage();
      
      // Create download link
      const link = document.createElement('a');
      link.download = `ghostlayer-transformation-${Date.now()}.png`;
      link.href = imageDataUrl;
      link.click();
      
      analytics.trackShare('download');
    } finally {
      setIsGeneratingImage(false);
    }
  };

  return (
    <Card className="p-6 bg-gradient-to-r from-purple-500/10 to-blue-500/10 border-purple-500/20">
      <div className="flex items-center gap-3 mb-4">
        <div className="p-2 bg-purple-500/20 rounded-lg">
          <Share2 className="w-5 h-5 text-purple-400" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Share Your Transformation</h3>
          <p className="text-gray-400 text-sm">Show the world how GhostLayer improved your text</p>
        </div>
      </div>

      {/* Stats showcase */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center p-3 bg-white/5 rounded-lg">
          <TrendingUp className="w-6 h-6 text-green-400 mx-auto mb-1" />
          <div className="text-xl font-bold text-white">{result.improvementScore}%</div>
          <div className="text-xs text-gray-400">More Human</div>
        </div>
        <div className="text-center p-3 bg-white/5 rounded-lg">
          <Sparkles className="w-6 h-6 text-blue-400 mx-auto mb-1" />
          <div className="text-xl font-bold text-white">{result.processingTime}ms</div>
          <div className="text-xs text-gray-400">Processing</div>
        </div>
        <div className="text-center p-3 bg-white/5 rounded-lg">
          <Users className="w-6 h-6 text-purple-400 mx-auto mb-1" />
          <div className="text-xl font-bold text-white">{result.variations?.length || 1}</div>
          <div className="text-xs text-gray-400">Variations</div>
        </div>
      </div>

      {/* Social sharing buttons */}
      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-3">
          <Button
            onClick={() => shareToSocial('twitter')}
            disabled={isGeneratingImage}
            className="bg-blue-500 hover:bg-blue-600 text-white"
          >
            <Twitter className="w-4 h-4 mr-2" />
            Twitter
          </Button>
          <Button
            onClick={() => shareToSocial('linkedin')}
            disabled={isGeneratingImage}
            className="bg-blue-700 hover:bg-blue-800 text-white"
          >
            <Linkedin className="w-4 h-4 mr-2" />
            LinkedIn
          </Button>
        </div>
        
        <div className="grid grid-cols-2 gap-3">
          <Button
            onClick={copyShareableLink}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-white/10"
          >
            {copiedLink ? (
              <CheckCircle className="w-4 h-4 mr-2" />
            ) : (
              <Copy className="w-4 h-4 mr-2" />
            )}
            {copiedLink ? 'Copied!' : 'Copy Link'}
          </Button>
          <Button
            onClick={downloadShareableImage}
            disabled={isGeneratingImage}
            variant="outline"
            className="border-gray-600 text-gray-300 hover:bg-white/10"
          >
            <Download className="w-4 h-4 mr-2" />
            {isGeneratingImage ? 'Generating...' : 'Download Image'}
          </Button>
        </div>
      </div>

      {/* Hidden canvas for image generation */}
      <canvas ref={canvasRef} style={{ display: 'none' }} />

      {/* Share Incentive */}
      {incentiveMessage && (
        <div className="mt-6 p-4 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg border border-blue-500/20">
          <div className="flex items-center gap-2 mb-2">
            <Gift className="w-4 h-4 text-blue-400" />
            <span className="text-blue-400 font-semibold text-sm">Share Rewards</span>
          </div>
          <p className="text-gray-300 text-sm mb-3">
            {incentiveMessage}
          </p>
          {session?.user?.id && (
            <div className="flex items-center gap-2 text-xs text-gray-400">
              <Users className="w-3 h-3" />
              <span>Total shares: {shareRewardManager.getUserStats(session.user.id).totalShares}</span>
              {shareRewardManager.hasPremiumAccess(session.user.id) && (
                <Badge className="bg-yellow-500/20 text-yellow-400 ml-2">
                  <Crown className="w-3 h-3 mr-1" />
                  Premium Active
                </Badge>
              )}
            </div>
          )}
        </div>
      )}

      {/* Viral call to action */}
      <div className="mt-4 p-4 bg-gradient-to-r from-yellow-500/10 to-orange-500/10 rounded-lg border border-yellow-500/20">
        <div className="flex items-center gap-2 mb-2">
          <Sparkles className="w-4 h-4 text-yellow-400" />
          <span className="text-yellow-400 font-semibold text-sm">Viral Challenge</span>
        </div>
        <p className="text-gray-300 text-sm">
          Share your transformation and tag 3 friends who need better AI content!
          The most impressive before/after gets featured on our homepage.
        </p>
      </div>

      {/* Reward Modal */}
      {showRewardModal && earnedRewards.length > 0 && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setShowRewardModal(false)}>
          <div className="bg-gray-900 p-6 rounded-lg border border-gray-700 max-w-md mx-4" onClick={e => e.stopPropagation()}>
            <div className="text-center">
              <div className="w-16 h-16 bg-yellow-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Gift className="w-8 h-8 text-yellow-400" />
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Rewards Earned! 🎉</h3>
              <div className="space-y-3 mb-6">
                {earnedRewards.map((reward, index) => (
                  <div key={index} className="p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center justify-center gap-2 mb-1">
                      {reward.type === 'credits' && <Coins className="w-4 h-4 text-green-400" />}
                      {reward.type === 'premium_unlock' && <Crown className="w-4 h-4 text-yellow-400" />}
                      {reward.type === 'feature_unlock' && <Sparkles className="w-4 h-4 text-purple-400" />}
                      <span className="text-white font-medium">
                        {reward.type === 'credits' && `+${reward.amount} Credits`}
                        {reward.type === 'premium_unlock' && `Premium Unlocked`}
                        {reward.type === 'feature_unlock' && `Feature Unlocked`}
                      </span>
                    </div>
                    <p className="text-gray-300 text-sm">{reward.description}</p>
                  </div>
                ))}
              </div>
              <Button onClick={() => setShowRewardModal(false)} className="bg-blue-600 hover:bg-blue-700">
                Awesome!
              </Button>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}
