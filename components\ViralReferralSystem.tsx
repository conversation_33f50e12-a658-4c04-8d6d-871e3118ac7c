'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Gift, Users, Share2, Copy, Check, X } from 'lucide-react';
import { analytics } from '@/lib/analytics';

interface ViralReferralSystemProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function ViralReferralSystem({ isOpen, onClose }: ViralReferralSystemProps) {
  const [referralCode, setReferralCode] = useState('GHOST-USER123');
  const [copied, setCopied] = useState(false);

  // Handle Escape key press
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when modal is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  const copyReferralCode = async () => {
    try {
      await navigator.clipboard.writeText(`https://ghostlayer.app/ref/${referralCode}`);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      analytics.trackShare('referral_code_copied');
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={handleBackdropClick}
    >
      <Card className="bg-white/5 backdrop-blur-lg border-white/10 max-w-2xl w-full mx-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-white">
            <div className="p-2 bg-pink-500/20 rounded-lg">
              <Gift className="w-5 h-5 text-pink-400" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-semibold">Refer Friends & Earn Credits</h3>
              <p className="text-gray-400 text-sm font-normal">Get premium credits by sharing GhostLayer</p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-400 hover:text-white"
            >
              <X className="w-4 h-4" />
            </Button>
          </CardTitle>
        </CardHeader>
      
      <CardContent>
        <div className="space-y-6">
          {/* Referral Link */}
          <div className="p-6 bg-gradient-to-r from-pink-500/10 to-purple-500/10 rounded-lg border border-pink-500/20">
            <h4 className="text-white font-semibold mb-3">Your Referral Link</h4>
            <div className="flex gap-2 mb-4">
              <input
                type="text"
                value={`https://ghostlayer.app/ref/${referralCode}`}
                readOnly
                className="flex-1 p-3 bg-black/30 border border-gray-600 rounded text-white text-sm"
              />
              <Button onClick={copyReferralCode} className="bg-pink-600 hover:bg-pink-700">
                {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
              </Button>
            </div>
            <p className="text-gray-300 text-sm mb-4">
              Share this link and earn <strong className="text-pink-400">100 credits</strong> for each friend who signs up!
            </p>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="p-4 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-blue-400">12</div>
              <div className="text-gray-400 text-sm">Referrals</div>
            </div>
            <div className="p-4 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-green-400">1,200</div>
              <div className="text-gray-400 text-sm">Credits Earned</div>
            </div>
            <div className="p-4 bg-white/5 rounded-lg text-center">
              <div className="text-2xl font-bold text-purple-400">8</div>
              <div className="text-gray-400 text-sm">Active Users</div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="p-4 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-lg border border-blue-500/20">
            <div className="flex items-center gap-2 mb-2">
              <Users className="w-4 h-4 text-blue-400" />
              <span className="text-blue-400 font-semibold text-sm">Unlock Premium Features</span>
            </div>
            <p className="text-gray-300 text-sm mb-3">
              Refer friends to earn credits and unlock premium humanization features like Heavy intensity mode!
            </p>
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Share2 className="w-4 h-4 mr-2" />
              Share Now
            </Button>
          </div>

          {/* Close Button */}
          <div className="flex justify-end pt-4 border-t border-gray-700">
            <Button
              onClick={onClose}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-800"
            >
              Close
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
    </div>
  );
}
