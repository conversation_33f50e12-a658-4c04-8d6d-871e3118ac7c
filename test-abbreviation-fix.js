/**
 * Test script to verify abbreviation preservation in text processing
 * Run with: node test-abbreviation-fix.js
 */

// Import the text processor
const { processTextDirectly } = require('./lib/textProcessor.ts');

// Test cases for abbreviation preservation
const testCases = [
  {
    name: 'Basic tech abbreviations',
    input: 'AI and LLM are transforming the tech industry. API integration is crucial.',
    expectedAbbreviations: ['AI', 'LLM', 'API'],
    description: 'Common tech abbreviations should remain unchanged'
  },
  {
    name: 'Business role abbreviations',
    input: 'The CEO announced that our CTO will lead the IT initiative with HR support.',
    expectedAbbreviations: ['CEO', 'CTO', 'IT', 'HR'],
    description: 'Business role abbreviations should be preserved'
  },
  {
    name: 'Web technology abbreviations',
    input: 'HTML and CSS files need optimization. JSON and XML parsing is important.',
    expectedAbbreviations: ['HTML', 'CSS', 'JSON', 'XML'],
    description: 'Web technology abbreviations should be preserved'
  },
  {
    name: 'Mixed case with abbreviations',
    input: 'JavaScript uses API calls to process HTML. The UI/UX design needs CSS.',
    expectedAbbreviations: ['JavaScript', 'API', 'HTML', 'UI', 'UX', 'CSS'],
    description: 'Mixed case terms and abbreviations should be preserved'
  },
  {
    name: 'Organization abbreviations',
    input: 'NASA and FBI work with CIA on AI projects. The USA and EU collaborate.',
    expectedAbbreviations: ['NASA', 'FBI', 'CIA', 'AI', 'USA', 'EU'],
    description: 'Organization abbreviations should be preserved'
  },
  {
    name: 'File format abbreviations',
    input: 'Convert PDF to CSV format. PNG and JPEG images need compression.',
    expectedAbbreviations: ['PDF', 'CSV', 'PNG', 'JPEG'],
    description: 'File format abbreviations should be preserved'
  },
  {
    name: 'Technology platform abbreviations',
    input: 'iOS and Android apps use GPS. WiFi and Bluetooth are essential.',
    expectedAbbreviations: ['iOS', 'Android', 'GPS', 'WiFi', 'Bluetooth'],
    description: 'Technology platform abbreviations should be preserved'
  }
];

console.log('🧪 Testing Abbreviation Preservation Fix\n');

let totalTests = 0;
let passedTests = 0;
let failedTests = 0;

testCases.forEach((testCase, index) => {
  console.log(`\n📝 Test ${index + 1}: ${testCase.name}`);
  console.log(`Input: "${testCase.input}"`);
  
  try {
    const result = processTextDirectly(testCase.input, {
      intensity: 'medium',
      style: 'balanced',
      language: 'en'
    });
    
    console.log(`Output: "${result.humanizedText}"`);
    
    // Check each expected abbreviation
    const preservedAbbreviations = [];
    const missingAbbreviations = [];
    
    testCase.expectedAbbreviations.forEach(abbrev => {
      const regex = new RegExp(`\\b${abbrev}\\b`, 'g');
      if (regex.test(result.humanizedText)) {
        preservedAbbreviations.push(abbrev);
      } else {
        missingAbbreviations.push(abbrev);
      }
    });
    
    // Check for unreplaced placeholders
    const placeholders = result.humanizedText.match(/__[A-Z_]+\d+__/g);
    
    const testPassed = missingAbbreviations.length === 0 && !placeholders;
    totalTests++;
    
    if (testPassed) {
      passedTests++;
      console.log('✅ PASSED');
    } else {
      failedTests++;
      console.log('❌ FAILED');
      
      if (missingAbbreviations.length > 0) {
        console.log(`   Missing abbreviations: ${missingAbbreviations.join(', ')}`);
      }
      
      if (placeholders) {
        console.log(`   Unreplaced placeholders: ${placeholders.join(', ')}`);
      }
    }
    
    console.log(`   Preserved: ${preservedAbbreviations.join(', ')}`);
    
  } catch (error) {
    totalTests++;
    failedTests++;
    console.log('❌ ERROR:', error.message);
  }
});

console.log('\n📊 Test Results Summary');
console.log('========================');
console.log(`Total Tests: ${totalTests}`);
console.log(`Passed: ${passedTests}`);
console.log(`Failed: ${failedTests}`);
console.log(`Success Rate: ${totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0}%`);

if (failedTests === 0) {
  console.log('\n🎉 All tests passed! Abbreviation preservation is working correctly.');
} else {
  console.log('\n⚠️  Some tests failed. Please review the implementation.');
}
