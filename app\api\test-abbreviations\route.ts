import { NextRequest, NextResponse } from 'next/server';
import { processTextDirectly } from '@/lib/textProcessor';

export async function POST(request: NextRequest) {
  const testCases = [
    {
      name: 'Basic tech abbreviations',
      input: 'AI and LLM are transforming the tech industry. API integration is crucial.',
      expectedAbbreviations: ['AI', 'LLM', 'API'],
    },
    {
      name: 'Business role abbreviations',
      input: 'The CEO announced that our CTO will lead the IT initiative with HR support.',
      expectedAbbreviations: ['CEO', 'CTO', 'IT', 'HR'],
    },
    {
      name: 'Web technology abbreviations',
      input: 'HTML and CSS files need optimization. JSON and XML parsing is important.',
      expectedAbbreviations: ['HTML', 'CSS', 'JSON', 'XML'],
    },
    {
      name: 'Mixed case with abbreviations',
      input: 'JavaScript uses API calls to process HTML. The UI/UX design needs CSS.',
      expectedAbbreviations: ['JavaScript', 'API', 'HTML', 'UI', 'UX', 'CSS'],
    },
    {
      name: 'Organization abbreviations',
      input: 'NASA and FBI work with CIA on AI projects. The USA and EU collaborate.',
      expectedAbbreviations: ['NASA', 'FBI', 'CIA', 'AI', 'USA', 'EU'],
    },
  ];

  const results = [];

  for (const testCase of testCases) {
    try {
      const result = processTextDirectly(testCase.input, {
        intensity: 'medium',
        style: 'balanced',
        language: 'en'
      });

      // Check each expected abbreviation
      const preservedAbbreviations = [];
      const missingAbbreviations = [];

      testCase.expectedAbbreviations.forEach(abbrev => {
        const regex = new RegExp(`\\b${abbrev}\\b`, 'g');
        if (regex.test(result.humanizedText)) {
          preservedAbbreviations.push(abbrev);
        } else {
          missingAbbreviations.push(abbrev);
        }
      });

      // Check for unreplaced placeholders
      const placeholders = result.humanizedText.match(/__[A-Z_]+\d+__/g);

      const testPassed = missingAbbreviations.length === 0 && !placeholders;

      results.push({
        ...testCase,
        output: result.humanizedText,
        preservedAbbreviations,
        missingAbbreviations,
        placeholders: placeholders || [],
        passed: testPassed,
        improvementScore: result.improvementScore
      });
    } catch (error) {
      results.push({
        ...testCase,
        error: error.message,
        passed: false
      });
    }
  }

  const passedTests = results.filter(r => r.passed).length;
  const totalTests = results.length;

  return NextResponse.json({
    summary: {
      totalTests,
      passedTests,
      failedTests: totalTests - passedTests,
      successRate: totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0
    },
    results
  });
}
