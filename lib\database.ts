/**
 * Database client for Ghost<PERSON>ayer
 * Provides a simple interface for database operations
 */

// For development, we'll use a simple in-memory store
// In production, this would connect to PostgreSQL
interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  tier: string;
  credits: number;
}

interface OAuthAccount {
  id: string;
  userId: string;
  provider: string;
  providerAccountId: string;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: number;
  tokenType?: string;
  scope?: string;
  createdAt: Date;
  updatedAt: Date;
}

// In-memory storage for development
const users = new Map<string, User>();
const accounts = new Map<string, OAuthAccount>();

// Initialize with demo user
users.set('demo-user-1', {
  id: 'demo-user-1',
  email: '<EMAIL>',
  name: 'Demo User',
  tier: 'free',
  credits: 100
});

export class DatabaseClient {
  // User operations
  async findUserByEmail(email: string): Promise<User | null> {
    for (const user of users.values()) {
      if (user.email === email) {
        return user;
      }
    }
    return null;
  }

  async findUserById(id: string): Promise<User | null> {
    return users.get(id) || null;
  }

  async createUser(userData: Omit<User, 'id'>): Promise<User> {
    const id = `user-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const user: User = { id, ...userData };
    users.set(id, user);
    return user;
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    const user = users.get(id);
    if (!user) return null;
    
    const updatedUser = { ...user, ...updates };
    users.set(id, updatedUser);
    return updatedUser;
  }

  // OAuth account operations
  async findAccountByProvider(userId: string, provider: string): Promise<OAuthAccount | null> {
    for (const account of accounts.values()) {
      if (account.userId === userId && account.provider === provider) {
        return account;
      }
    }
    return null;
  }

  async createOrUpdateAccount(accountData: {
    userId: string;
    provider: string;
    providerAccountId: string;
    accessToken?: string;
    refreshToken?: string;
    expiresAt?: number;
    tokenType?: string;
    scope?: string;
  }): Promise<OAuthAccount> {
    // Check if account already exists
    const existingAccount = await this.findAccountByProvider(accountData.userId, accountData.provider);
    
    if (existingAccount) {
      // Update existing account
      const updatedAccount: OAuthAccount = {
        ...existingAccount,
        ...accountData,
        updatedAt: new Date()
      };
      accounts.set(existingAccount.id, updatedAccount);
      return updatedAccount;
    } else {
      // Create new account
      const id = `account-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      const account: OAuthAccount = {
        id,
        ...accountData,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      accounts.set(id, account);
      return account;
    }
  }

  async deleteAccount(userId: string, provider: string): Promise<boolean> {
    for (const [id, account] of accounts.entries()) {
      if (account.userId === userId && account.provider === provider) {
        accounts.delete(id);
        return true;
      }
    }
    return false;
  }

  // Google Docs specific operations
  async getGoogleDocsToken(userId: string): Promise<{ accessToken: string; refreshToken?: string; expiresAt?: number } | null> {
    const account = await this.findAccountByProvider(userId, 'google-docs');
    if (!account || !account.accessToken) {
      return null;
    }

    // Check if token is expired
    if (account.expiresAt && account.expiresAt < Date.now()) {
      // Token is expired, try to refresh if we have a refresh token
      if (account.refreshToken) {
        try {
          const refreshedTokens = await this.refreshGoogleToken(account.refreshToken);
          if (refreshedTokens) {
            // Update the account with new tokens
            await this.createOrUpdateAccount({
              userId,
              provider: 'google-docs',
              providerAccountId: account.providerAccountId,
              accessToken: refreshedTokens.accessToken,
              refreshToken: refreshedTokens.refreshToken || account.refreshToken,
              expiresAt: refreshedTokens.expiresAt,
              tokenType: 'Bearer',
              scope: account.scope
            });
            return refreshedTokens;
          }
        } catch (error) {
          console.error('Failed to refresh Google token:', error);
          return null;
        }
      }
      return null;
    }

    return {
      accessToken: account.accessToken,
      refreshToken: account.refreshToken,
      expiresAt: account.expiresAt
    };
  }

  async saveGoogleDocsToken(userId: string, tokenData: {
    accessToken: string;
    refreshToken?: string;
    expiresIn?: number;
    scope?: string;
  }): Promise<void> {
    const expiresAt = tokenData.expiresIn ? Date.now() + (tokenData.expiresIn * 1000) : undefined;
    
    await this.createOrUpdateAccount({
      userId,
      provider: 'google-docs',
      providerAccountId: userId, // Use userId as provider account ID for simplicity
      accessToken: tokenData.accessToken,
      refreshToken: tokenData.refreshToken,
      expiresAt,
      tokenType: 'Bearer',
      scope: tokenData.scope
    });
  }

  private async refreshGoogleToken(refreshToken: string): Promise<{
    accessToken: string;
    refreshToken?: string;
    expiresAt: number;
  } | null> {
    try {
      const response = await fetch('https://oauth2.googleapis.com/token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: process.env.GOOGLE_CLIENT_ID || '',
          client_secret: process.env.GOOGLE_CLIENT_SECRET || '',
          refresh_token: refreshToken,
          grant_type: 'refresh_token',
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to refresh token');
      }

      const data = await response.json();
      return {
        accessToken: data.access_token,
        refreshToken: data.refresh_token, // May not be provided
        expiresAt: Date.now() + (data.expires_in * 1000)
      };
    } catch (error) {
      console.error('Token refresh failed:', error);
      return null;
    }
  }
}

// Export singleton instance
export const db = new DatabaseClient();
