import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

/**
 * Community Data API
 * Provides community posts, leaderboard, and social features
 */

interface CommunityPost {
  id: string;
  username: string;
  avatar: string;
  originalText: string;
  humanizedText: string;
  improvementScore: number;
  likes: number;
  comments: number;
  shares: number;
  timestamp: string;
  badges: string[];
  isLiked?: boolean;
}

interface LeaderboardEntry {
  rank: number;
  username: string;
  score: number;
  avatar: string;
  isCurrentUser?: boolean;
}

// Mock community data - in production, this would come from database
function generateCommunityPosts(currentUserId?: string): CommunityPost[] {
  const posts: CommunityPost[] = [
    {
      id: '1',
      username: 'ContentCreator_Pro',
      avatar: '👨‍💼',
      originalText: 'The implementation of artificial intelligence in modern business processes has significantly enhanced operational efficiency and productivity metrics across various industry sectors.',
      humanizedText: 'AI has revolutionized how businesses operate today. Companies across different industries are seeing real improvements in efficiency and productivity thanks to smart automation.',
      improvementScore: 94,
      likes: 156,
      comments: 23,
      shares: 45,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
      badges: ['🏆 Top Performer', '🔥 Viral Post'],
      isLiked: false
    },
    {
      id: '2',
      username: 'TechWriter_Sarah',
      avatar: '👩‍💻',
      originalText: 'Machine learning algorithms demonstrate exceptional capabilities in pattern recognition and data analysis tasks.',
      humanizedText: 'Machine learning is incredibly good at spotting patterns and analyzing data in ways that help us make better decisions.',
      improvementScore: 87,
      likes: 89,
      comments: 12,
      shares: 28,
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(), // 4 hours ago
      badges: ['✨ Rising Star'],
      isLiked: true
    },
    {
      id: '3',
      username: 'BlogMaster_Alex',
      avatar: '📝',
      originalText: 'The utilization of advanced natural language processing techniques enables sophisticated text transformation capabilities.',
      humanizedText: 'Advanced NLP technology makes it possible to transform text in really sophisticated ways that sound natural and engaging.',
      improvementScore: 91,
      likes: 134,
      comments: 18,
      shares: 37,
      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
      badges: ['🎯 Precision Master'],
      isLiked: false
    }
  ];

  return posts;
}

function generateLeaderboard(currentUserId?: string, currentUserStats?: any): LeaderboardEntry[] {
  const leaderboard: LeaderboardEntry[] = [
    { rank: 1, username: 'HumanizeKing', score: 15420, avatar: '👑' },
    { rank: 2, username: 'TextMaster_Pro', score: 14890, avatar: '🥈' },
    { rank: 3, username: 'ContentWizard', score: 13750, avatar: '🥉' },
    { rank: 4, username: 'WriteRight', score: 12340, avatar: '⭐' },
    { rank: 5, username: 'HumanizeHero', score: 11890, avatar: '🚀' },
  ];

  // Add current user to leaderboard if they have stats
  if (currentUserStats) {
    leaderboard.push({
      rank: currentUserStats.rank || Math.floor(Math.random() * 500) + 100,
      username: currentUserStats.username || 'You',
      score: currentUserStats.totalScore || 1000,
      avatar: currentUserStats.avatar || '👤',
      isCurrentUser: true
    });

    // Sort by score descending
    leaderboard.sort((a, b) => b.score - a.score);
    
    // Update ranks
    leaderboard.forEach((entry, index) => {
      entry.rank = index + 1;
    });
  }

  return leaderboard;
}

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'posts';

    switch (type) {
      case 'posts':
        const posts = generateCommunityPosts(session?.user?.id);
        return NextResponse.json({
          success: true,
          data: {
            posts,
            totalPosts: posts.length,
            hasMore: false // In production, implement pagination
          }
        });

      case 'leaderboard':
        // Get current user stats for leaderboard positioning
        let currentUserStats = null;
        if (session?.user) {
          // In production, fetch from database
          currentUserStats = {
            rank: Math.floor(Math.random() * 500) + 100,
            username: session.user.name || session.user.email?.split('@')[0] || 'User',
            totalScore: (session.user.credits || 100) * 2,
            avatar: '👤'
          };
        }

        const leaderboard = generateLeaderboard(session?.user?.id, currentUserStats);
        
        return NextResponse.json({
          success: true,
          data: {
            leaderboard: leaderboard.slice(0, 10), // Top 10 + current user
            currentUserRank: currentUserStats?.rank || null,
            totalUsers: 2547 // Mock total users
          }
        });

      case 'stats':
        return NextResponse.json({
          success: true,
          data: {
            totalUsers: 2547,
            totalTransformations: 45892,
            avgImprovementScore: 83.2,
            activeToday: 342,
            topCountries: ['United States', 'United Kingdom', 'Canada', 'Australia', 'Germany']
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid type parameter' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Community API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { action, postId } = body;

    switch (action) {
      case 'like':
        if (!postId) {
          return NextResponse.json(
            { error: 'Missing postId' },
            { status: 400 }
          );
        }

        // In production, update database
        return NextResponse.json({
          success: true,
          data: {
            postId,
            liked: true,
            newLikeCount: Math.floor(Math.random() * 200) + 50
          }
        });

      case 'share':
        if (!postId) {
          return NextResponse.json(
            { error: 'Missing postId' },
            { status: 400 }
          );
        }

        // In production, create share record
        return NextResponse.json({
          success: true,
          data: {
            postId,
            shared: true,
            shareUrl: `${process.env.NEXTAUTH_URL}/community/post/${postId}`
          }
        });

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Community POST API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
