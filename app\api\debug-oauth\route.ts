import { NextRequest, NextResponse } from 'next/server';

/**
 * Debug endpoint to verify OAuth configuration
 * Access at: http://localhost:3000/api/debug-oauth
 */
export async function GET(request: NextRequest) {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const callbackUrl = `${baseUrl}/api/auth/callback/google`;
  
  return NextResponse.json({
    message: 'OAuth Configuration Debug Info',
    configuration: {
      NEXTAUTH_URL: process.env.NEXTAUTH_URL,
      expectedGoogleCallback: callbackUrl,
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecretConfigured: !!process.env.GOOGLE_CLIENT_SECRET,
      nodeEnv: process.env.NODE_ENV,
    },
    instructions: {
      step1: 'Copy the expectedGoogleCallback URL below',
      step2: 'Go to Google Cloud Console > APIs & Services > Credentials',
      step3: 'Find your OAuth 2.0 Client ID and click to edit',
      step4: 'Add the expectedGoogleCallback URL to Authorized redirect URIs',
      step5: 'Make sure there are no trailing slashes or extra characters',
      step6: 'Save changes and wait 5-10 minutes for propagation'
    },
    troubleshooting: {
      commonMistakes: [
        'Using HTTPS instead of HTTP for localhost',
        'Adding trailing slash to callback URL',
        'Case sensitivity in the URL',
        'Extra spaces in the URL',
        'Wrong OAuth client ID being edited'
      ],
      verificationSteps: [
        'Clear browser cache and cookies',
        'Restart development server',
        'Test in incognito/private window',
        'Check browser network tab for actual redirect_uri parameter'
      ]
    }
  }, {
    headers: {
      'Content-Type': 'application/json',
    }
  });
}
