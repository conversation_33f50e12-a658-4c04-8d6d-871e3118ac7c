'use client';

import { useSearchParams } from 'next/navigation';
import { <PERSON>ertCircle, ArrowLeft, Settings } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

const errorMessages = {
  Configuration: {
    title: 'OAuth Configuration Error',
    description: 'The OAuth provider is not properly configured.',
    solution: 'Please check the Google OAuth setup in Google Cloud Console.',
    action: 'View Setup Guide'
  },
  OAuthCallback: {
    title: 'OAuth Redirect URI Mismatch',
    description: 'The redirect URI in Google Cloud Console doesn\'t match the expected callback URL.',
    solution: 'Please verify the authorized redirect URIs in Google Cloud Console match exactly: http://localhost:3000/api/auth/callback/google',
    action: 'Fix Redirect URI'
  },
  AccessDenied: {
    title: 'Access Denied',
    description: 'You denied access to your account.',
    solution: 'Please try signing in again and grant the necessary permissions.',
    action: 'Try Again'
  },
  Verification: {
    title: 'Email Verification Required',
    description: 'Your email address needs to be verified.',
    solution: 'Please check your email and verify your account.',
    action: 'Resend Email'
  },
  Default: {
    title: 'Authentication Error',
    description: 'An error occurred during authentication.',
    solution: 'Please try signing in again or contact support if the problem persists.',
    action: 'Try Again'
  }
};

export default function AuthError() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error') || 'Default';
  
  const errorInfo = errorMessages[error as keyof typeof errorMessages] || errorMessages.Default;

  const handleSetupGuide = () => {
    // Open the setup guide
    window.open('/GOOGLE_OAUTH_SETUP.md', '_blank');
  };

  const handleRedirectUriFix = () => {
    // Open the redirect URI fix guide
    window.open('/OAUTH_REDIRECT_URI_FIX.md', '_blank');
  };

  const handleDebugInfo = () => {
    // Open debug endpoint
    window.open('/api/debug-oauth', '_blank');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md bg-gray-900/90 backdrop-blur-lg border-gray-700">
        <CardHeader className="text-center">
          <div className="mx-auto w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-4">
            <AlertCircle className="w-8 h-8 text-red-400" />
          </div>
          <CardTitle className="text-white text-xl">{errorInfo.title}</CardTitle>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="text-center space-y-3">
            <p className="text-gray-300">{errorInfo.description}</p>
            <p className="text-sm text-gray-400">{errorInfo.solution}</p>
          </div>

          {error === 'Configuration' && (
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <Settings className="w-5 h-5 text-yellow-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-yellow-300 font-medium mb-1">Setup Required</p>
                  <p className="text-yellow-200/80">
                    Google OAuth credentials are not configured. Follow the setup guide to fix this issue.
                  </p>
                </div>
              </div>
            </div>
          )}

          {error === 'OAuthCallback' && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="w-5 h-5 text-red-400 mt-0.5" />
                <div className="text-sm">
                  <p className="text-red-300 font-medium mb-1">Redirect URI Mismatch</p>
                  <p className="text-red-200/80 mb-2">
                    The redirect URI in Google Cloud Console doesn't match the expected callback URL.
                  </p>
                  <div className="bg-gray-800/50 p-2 rounded text-xs font-mono text-green-400">
                    http://localhost:3000/api/auth/callback/google
                  </div>
                  <p className="text-red-200/80 mt-2 text-xs">
                    Copy this URL and add it to "Authorized redirect URIs" in Google Cloud Console.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-3">
            {error === 'Configuration' ? (
              <Button
                onClick={handleSetupGuide}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                <Settings className="w-4 h-4 mr-2" />
                View Setup Guide
              </Button>
            ) : error === 'OAuthCallback' ? (
              <>
                <Button
                  onClick={handleRedirectUriFix}
                  className="w-full bg-red-600 hover:bg-red-700"
                >
                  <AlertCircle className="w-4 h-4 mr-2" />
                  Fix Redirect URI
                </Button>
                <Button
                  onClick={handleDebugInfo}
                  variant="outline"
                  className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Debug Info
                </Button>
              </>
            ) : (
              <Link href="/auth/signin">
                <Button className="w-full bg-blue-600 hover:bg-blue-700">
                  {errorInfo.action}
                </Button>
              </Link>
            )}
            
            <Link href="/">
              <Button variant="outline" className="w-full border-gray-600 text-gray-300 hover:bg-gray-800">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </Link>
          </div>

          <div className="text-center">
            <p className="text-xs text-gray-500">
              Error Code: {error}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
