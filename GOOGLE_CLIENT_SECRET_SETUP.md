# 🔑 Google Client Secret Setup Guide

## Current Status
✅ **NEXTAUTH_SECRET**: Generated and configured  
❌ **GOOGLE_CLIENT_SECRET**: Still needs to be configured  
✅ **GOOGLE_CLIENT_ID**: Already configured  

## Step-by-Step Instructions

### 1. Access Google Cloud Console
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Sign in with your Google account
3. Select your project (or create one if needed)

### 2. Navigate to Credentials
1. In the left sidebar, click **"APIs & Services"**
2. Click **"Credentials"**
3. You should see your OAuth 2.0 Client ID: `************-7jro0haaqqs8acdkvd3rac7mopojca36.apps.googleusercontent.com`

### 3. Get the Client Secret
1. Click on the OAuth 2.0 Client ID entry
2. You'll see both:
   - **Client ID**: `************-7jro0haaqqs8acdkvd3rac7mopojca36.apps.googleusercontent.com`
   - **Client Secret**: `GOCSPX-xxxxxxxxxxxxxxxxxxxxxxxxx` (this is what you need)
3. Click the **copy button** next to the Client Secret

### 4. Update .env.local
Replace the placeholder in your `.env.local` file:
```env
GOOGLE_CLIENT_SECRET=GOCSPX-your-actual-client-secret-here
```

### 5. Verify OAuth Configuration
Ensure these settings are correct in Google Cloud Console:

**Authorized JavaScript origins:**
```
http://localhost:3000
```

**Authorized redirect URIs:**
```
http://localhost:3000/api/auth/callback/google
```

### 6. OAuth Consent Screen
1. Go to **"OAuth consent screen"**
2. Ensure these are configured:
   - **App name**: GhostLayer
   - **User support email**: Your email
   - **Developer contact**: Your email
   - **Authorized domains**: `localhost` (for development)

### 7. Test Users (Development Mode)
1. In **"OAuth consent screen"** → **"Test users"**
2. Add your email address as a test user
3. Click **"Save"**

### 8. Restart Development Server
```bash
# Stop current server (Ctrl+C)
npm run dev
```

## Troubleshooting

### "Access blocked: This app's request is invalid"
- **Cause**: Missing or incorrect Client Secret
- **Solution**: Follow steps 3-4 above

### "redirect_uri_mismatch"
- **Cause**: Redirect URI doesn't match
- **Solution**: Ensure callback URL is exactly: `http://localhost:3000/api/auth/callback/google`

### "invalid_client"
- **Cause**: Wrong Client ID or Client Secret
- **Solution**: Double-check both values from Google Cloud Console

## Security Notes
- Never commit `.env.local` to version control
- Keep your Client Secret private
- Use different credentials for production

## Next Steps
Once both secrets are configured:
1. Test Google sign-in functionality
2. Verify user session creation
3. Test authentication flow end-to-end
