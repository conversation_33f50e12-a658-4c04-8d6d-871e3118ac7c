import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import SecurityProvider from '@/components/SecurityProvider';
import Providers from '@/components/Providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'GhostLayer - AI Text Humanization Tool',
    template: '%s | GhostLayer'
  },
  description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent. Advanced AI humanization technology for researchers, writers, and content creators.',
  keywords: [
    'AI text humanization',
    'content transformation',
    'AI detection bypass',
    'text paraphrasing',
    'AI writing tool',
    'content optimization',
    'text enhancement',
    'academic writing',
    'content creation',
    'AI content humanizer'
  ],
  authors: [{ name: 'GhostLayer Team' }],
  creator: '<PERSON><PERSON><PERSON><PERSON>',
  publisher: 'GhostLayer',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  alternates: {
    canonical: '/',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://ghostlayer.io.vn',
    title: 'GhostLayer - AI Text Humanization Tool',
    description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent. Advanced AI humanization technology for researchers, writers, and content creators.',
    siteName: 'GhostLayer',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'GhostLayer - AI Text Humanization Tool',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'GhostLayer - AI Text Humanization Tool',
    description: 'Transform AI-generated content into natural, human-like writing while preserving meaning and intent.',
    images: ['/og-image.jpg'],
    creator: '@ghostlayer',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
};

const structuredData = {
  "@context": "https://schema.org",
  "@type": "SoftwareApplication",
  "name": "GhostLayer",
  "description": "Transform AI-generated content into natural, human-like writing while preserving meaning and intent.",
  "url": "https://ghostlayer.io.vn",
  "applicationCategory": "BusinessApplication",
  "operatingSystem": "Web Browser",
  "offers": {
    "@type": "Offer",
    "price": "0",
    "priceCurrency": "USD"
  },
  "creator": {
    "@type": "Organization",
    "name": "GhostLayer Team"
  },
  "featureList": [
    "AI Text Humanization",
    "Content Transformation",
    "Multiple Writing Styles",
    "Real-time Processing",
    "Privacy-Focused"
  ],
  "screenshot": "https://ghostlayer.io.vn/screenshot.jpg"
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        {/* Preconnect to external domains */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="" />
        <link rel="preconnect" href="https://www.googletagmanager.com" />

        {/* Favicon and app icons */}
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/logo.jpg" type="image/jpeg" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.json" />

        {/* Theme color for mobile browsers */}
        <meta name="theme-color" content="#3b82f6" />
        <meta name="msapplication-TileColor" content="#3b82f6" />

        {/* PWA meta tags */}
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
        <meta name="apple-mobile-web-app-title" content="GhostLayer" />

        {/* Viewport meta for PWA */}
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, viewport-fit=cover" />

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(structuredData),
          }}
        />

        {/* Google Analytics */}
        <script async src="https://www.googletagmanager.com/gtag/js?id=G-LHNTYNHE5N"></script>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              function gtag(){dataLayer.push(arguments);}
              gtag('js', new Date());
              gtag('config', 'G-LHNTYNHE5N');
            `,
          }}
        />
      </head>
      <body className={inter.className}>
        <Providers>
          <SecurityProvider>
            {children}
          </SecurityProvider>
        </Providers>

        {/* Service Worker Registration */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);

                      // Check for updates
                      registration.addEventListener('updatefound', function() {
                        const newWorker = registration.installing;
                        newWorker.addEventListener('statechange', function() {
                          if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            // New version available
                            if (confirm('New version available! Reload to update?')) {
                              newWorker.postMessage({ type: 'SKIP_WAITING' });
                              window.location.reload();
                            }
                          }
                        });
                      });
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }

              // PWA install prompt
              let deferredPrompt;
              window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;

                // Check if banner was dismissed recently (within 7 days)
                const dismissedTime = localStorage.getItem('pwa-banner-dismissed');
                const sevenDaysInMs = 7 * 24 * 60 * 60 * 1000;

                if (dismissedTime && (Date.now() - parseInt(dismissedTime)) < sevenDaysInMs) {
                  console.log('PWA banner was recently dismissed, not showing again');
                  return;
                }

                // Check if already installed (basic check)
                if (window.matchMedia && window.matchMedia('(display-mode: standalone)').matches) {
                  console.log('App is already installed');
                  return;
                }

                // Show install button or banner - improved positioning and styling
                const installBanner = document.createElement('div');
                installBanner.id = 'ghostlayer-pwa-banner';
                installBanner.innerHTML = \`
                  <div style="
                    position: fixed;
                    bottom: 20px;
                    right: 20px;
                    width: 320px;
                    max-width: calc(100vw - 40px);
                    background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%);
                    color: white;
                    padding: 16px;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.4), 0 0 0 1px rgba(255,255,255,0.1);
                    z-index: 9999;
                    font-size: 14px;
                    backdrop-filter: blur(16px);
                    border: 1px solid rgba(255,255,255,0.1);
                    animation: slideInUp 0.3s ease-out;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                  ">
                    <style>
                      @keyframes slideInUp {
                        from { transform: translateY(100%); opacity: 0; }
                        to { transform: translateY(0); opacity: 1; }
                      }
                      @media (max-width: 640px) {
                        #ghostlayer-pwa-banner > div {
                          width: calc(100vw - 32px) !important;
                          bottom: 16px !important;
                          right: 16px !important;
                          left: 16px !important;
                          font-size: 13px !important;
                        }
                      }
                    </style>
                    <div style="display: flex; align-items: flex-start; justify-content: space-between; gap: 12px;">
                      <div style="flex: 1; min-width: 0;">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 4px;">
                          <span style="font-size: 16px;">📱</span>
                          <div style="font-weight: 600; font-size: 14px; color: #f1f5f9;">Install GhostLayer App</div>
                        </div>
                        <div style="font-size: 12px; opacity: 0.8; line-height: 1.4; color: #cbd5e1; margin-bottom: 12px;">
                          Get quick access, offline support, and enhanced performance
                        </div>
                        <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                          <button onclick="installPWA()" style="
                            background: linear-gradient(135deg, #3b82f6, #6366f1);
                            border: none;
                            color: white;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 12px;
                            font-weight: 600;
                            transition: all 0.2s ease;
                            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
                          " onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 8px rgba(59, 130, 246, 0.3)'">
                            Install Now
                          </button>
                          <button onclick="dismissPWABanner()" style="
                            background: rgba(255,255,255,0.1);
                            border: 1px solid rgba(255,255,255,0.2);
                            color: #cbd5e1;
                            padding: 8px 12px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 12px;
                            font-weight: 500;
                            transition: all 0.2s ease;
                          " onmouseover="this.style.background='rgba(255,255,255,0.15)'" onmouseout="this.style.background='rgba(255,255,255,0.1)'">
                            Maybe Later
                          </button>
                        </div>
                      </div>
                      <button onclick="dismissPWABanner()" style="
                        background: none;
                        border: none;
                        color: #94a3b8;
                        padding: 4px;
                        cursor: pointer;
                        font-size: 18px;
                        line-height: 1;
                        border-radius: 4px;
                        transition: all 0.2s ease;
                        flex-shrink: 0;
                      " onmouseover="this.style.color='#f1f5f9'; this.style.background='rgba(255,255,255,0.1)'" onmouseout="this.style.color='#94a3b8'; this.style.background='none'">
                        ✕
                      </button>
                    </div>
                  </div>
                \`;
                document.body.appendChild(installBanner);
              });

              function installPWA() {
                if (deferredPrompt) {
                  deferredPrompt.prompt();
                  deferredPrompt.userChoice.then((choiceResult) => {
                    if (choiceResult.outcome === 'accepted') {
                      console.log('User accepted the install prompt');
                      // Track successful installation
                      if (typeof gtag !== 'undefined') {
                        gtag('event', 'pwa_install_accepted', {
                          event_category: 'engagement',
                          event_label: 'PWA Installation Accepted'
                        });
                      }
                      // Remove banner after successful install
                      dismissPWABanner();
                    } else {
                      console.log('User dismissed the install prompt');
                      // Track dismissal
                      if (typeof gtag !== 'undefined') {
                        gtag('event', 'pwa_install_dismissed', {
                          event_category: 'engagement',
                          event_label: 'PWA Installation Dismissed'
                        });
                      }
                    }
                    deferredPrompt = null;
                  });
                } else {
                  // Fallback for browsers that don't support PWA installation
                  alert('PWA installation is not supported in this browser. You can manually add this site to your home screen.');
                }
              }

              function dismissPWABanner() {
                const banner = document.getElementById('ghostlayer-pwa-banner');
                if (banner) {
                  // Animate out
                  banner.style.animation = 'slideOutDown 0.3s ease-in forwards';
                  banner.style.setProperty('--slide-out', 'slideOutDown 0.3s ease-in forwards');

                  // Add slide out animation
                  const style = document.createElement('style');
                  style.textContent = \`
                    @keyframes slideOutDown {
                      from { transform: translateY(0); opacity: 1; }
                      to { transform: translateY(100%); opacity: 0; }
                    }
                  \`;
                  document.head.appendChild(style);

                  setTimeout(() => {
                    banner.remove();
                    style.remove();
                  }, 300);

                  // Store dismissal in localStorage to prevent showing again for 7 days
                  localStorage.setItem('pwa-banner-dismissed', Date.now().toString());

                  // Track dismissal
                  if (typeof gtag !== 'undefined') {
                    gtag('event', 'pwa_banner_dismissed', {
                      event_category: 'engagement',
                      event_label: 'PWA Banner Manually Dismissed'
                    });
                  }
                }
              }

              // Handle app installed
              window.addEventListener('appinstalled', (evt) => {
                console.log('GhostLayer was installed');
                // Track installation
                if (typeof gtag !== 'undefined') {
                  gtag('event', 'pwa_installed', {
                    event_category: 'engagement',
                    event_label: 'PWA Installation'
                  });
                }
              });
            `,
          }}
        />
      </body>
    </html>
  );
}