// File processing utilities for batch operations
import { processTextDirectly } from './textProcessor';
import { ProcessingOptions, ProcessingResult } from '@/types';

export interface FileProcessingResult {
  fileName: string;
  originalSize: number;
  processedSize: number;
  result: ProcessingResult;
  error?: string;
}

export interface BatchProcessingResult {
  results: FileProcessingResult[];
  totalFiles: number;
  successfulFiles: number;
  failedFiles: number;
  totalProcessingTime: number;
  averageImprovementScore: number;
}

// Supported file types
export const SUPPORTED_FILE_TYPES = {
  'text/plain': ['.txt'],
  'text/markdown': ['.md'],
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
  'application/pdf': ['.pdf'],
  'application/rtf': ['.rtf'],
  'application/vnd.oasis.opendocument.text': ['.odt']
};

// Extract text from different file formats
export async function extractTextFromFile(file: File): Promise<string> {
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  try {
    switch (fileType) {
      case 'text/plain':
      case 'text/markdown':
        return await readTextFile(file);
      
      case 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
        return await extractFromDocx(file);
      
      case 'application/pdf':
        return await extractFromPdf(file);
      
      case 'application/rtf':
        return await extractFromRtf(file);
      
      case 'application/vnd.oasis.opendocument.text':
        return await extractFromOdt(file);
      
      default:
        // Try to detect by file extension if MIME type is not recognized
        if (fileName.endsWith('.txt') || fileName.endsWith('.md')) {
          return await readTextFile(file);
        } else if (fileName.endsWith('.docx')) {
          return await extractFromDocx(file);
        } else if (fileName.endsWith('.pdf')) {
          return await extractFromPdf(file);
        } else if (fileName.endsWith('.rtf')) {
          return await extractFromRtf(file);
        } else if (fileName.endsWith('.odt')) {
          return await extractFromOdt(file);
        } else if (fileName.endsWith('.json')) {
          return await extractFromJson(file);
        } else if (fileName.endsWith('.csv')) {
          return await extractFromCsv(file);
        } else if (fileName.endsWith('.html') || fileName.endsWith('.htm')) {
          return await extractFromHtml(file);
        } else if (fileName.endsWith('.xml')) {
          return await extractFromXml(file);
        } else {
          // Try to read as plain text as last resort
          try {
            const content = await readTextFile(file);
            if (content && content.trim().length > 0) {
              return content;
            }
          } catch {
            // If plain text reading fails, throw the original error
          }
        }

        throw new Error(`Unsupported file type: ${fileType || 'unknown'}. Supported formats: .txt, .md, .docx, .pdf, .rtf, .odt, .json, .csv, .html, .xml`);
    }
  } catch (error) {
    throw new Error(`Failed to extract text from ${file.name}: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Read plain text files
async function readTextFile(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (content) {
        resolve(content);
      } else {
        reject(new Error('Failed to read file content'));
      }
    };
    reader.onerror = () => reject(new Error('File reading failed'));
    reader.readAsText(file);
  });
}

// Extract text from DOCX files (simplified - in production would use a proper library)
async function extractFromDocx(file: File): Promise<string> {
  try {
    // For now, we'll simulate DOCX extraction with better text extraction
    // In production, you'd use libraries like mammoth.js or docx-parser
    const arrayBuffer = await file.arrayBuffer();

    // Convert to Uint8Array for better processing
    const uint8Array = new Uint8Array(arrayBuffer);

    // Try to extract text using a more sophisticated approach
    let extractedText = '';

    // Look for XML content within the DOCX (which is a ZIP file)
    const decoder = new TextDecoder('utf-8', { ignoreBOM: true });
    const content = decoder.decode(uint8Array);

    // Extract text between XML tags (simplified approach)
    const textMatches = content.match(/<w:t[^>]*>([^<]+)<\/w:t>/g);
    if (textMatches) {
      extractedText = textMatches
        .map(match => match.replace(/<[^>]+>/g, ''))
        .join(' ')
        .replace(/\s+/g, ' ')
        .trim();
    }

    // Fallback: extract any readable text
    if (!extractedText) {
      const readableText = content.match(/[a-zA-Z0-9\s.,!?;:'"()-]{10,}/g);
      if (readableText) {
        extractedText = readableText
          .join(' ')
          .replace(/\s+/g, ' ')
          .trim();
      }
    }

    if (!extractedText || extractedText.length < 10) {
      throw new Error('No readable text found in DOCX file');
    }

    return extractedText;
  } catch (error) {
    throw new Error(`Failed to extract text from DOCX: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Extract text from PDF files (simplified - in production would use PDF.js or similar)
async function extractFromPdf(file: File): Promise<string> {
  // For now, we'll simulate PDF extraction
  // In production, you'd use libraries like PDF.js or pdf-parse
  
  // Simulate PDF text extraction
  const fileName = file.name;
  return `[PDF Content from ${fileName}]\n\nThis is simulated PDF text extraction. In production, this would use a proper PDF parsing library to extract the actual text content from the PDF file.\n\nThe extracted text would maintain formatting and structure while being suitable for humanization processing.`;
}

// Extract text from RTF files
async function extractFromRtf(file: File): Promise<string> {
  const content = await readTextFile(file);
  
  // Basic RTF text extraction (remove RTF control codes)
  let text = content
    .replace(/\\[a-z]+\d*\s?/g, '') // Remove RTF control words
    .replace(/[{}]/g, '') // Remove braces
    .replace(/\\\\/g, '\\') // Unescape backslashes
    .replace(/\\'/g, "'") // Unescape quotes
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
  
  return text || 'No readable text found in RTF file';
}

// Extract text from ODT files (simplified)
async function extractFromOdt(file: File): Promise<string> {
  // For now, we'll simulate ODT extraction
  // In production, you'd use libraries that can parse OpenDocument format
  const fileName = file.name;
  return `[ODT Content from ${fileName}]\n\nThis is simulated ODT text extraction. In production, this would use a proper OpenDocument parser to extract the actual text content from the ODT file.`;
}

// Extract text from JSON files
async function extractFromJson(file: File): Promise<string> {
  try {
    const content = await readTextFile(file);
    const parsed = JSON.parse(content);

    // Extract text values from JSON for processing
    const extractedText = extractTextFromJSON(parsed);
    return extractedText || JSON.stringify(parsed, null, 2);
  } catch (error) {
    throw new Error(`Failed to parse JSON file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Extract text from CSV files
async function extractFromCsv(file: File): Promise<string> {
  try {
    const content = await readTextFile(file);
    if (!content || content.trim().length === 0) {
      throw new Error('CSV file appears to be empty');
    }

    // Convert CSV to readable format
    const lines = content.split('\n');
    const processedLines = lines.map(line => {
      return line.split(',').join(' | ');
    });

    return processedLines.join('\n');
  } catch (error) {
    throw new Error(`Failed to process CSV file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Extract text from HTML files
async function extractFromHtml(file: File): Promise<string> {
  try {
    const content = await readTextFile(file);
    if (!content || content.trim().length === 0) {
      throw new Error('HTML file appears to be empty');
    }

    // Simple HTML text extraction (remove tags)
    let text = content.replace(/<script[^>]*>[\s\S]*?<\/script>/gi, ''); // Remove scripts
    text = text.replace(/<style[^>]*>[\s\S]*?<\/style>/gi, ''); // Remove styles
    text = text.replace(/<[^>]*>/g, ' '); // Remove all HTML tags
    text = text.replace(/\s+/g, ' ').trim(); // Clean up whitespace

    return text;
  } catch (error) {
    throw new Error(`Failed to process HTML file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Extract text from XML files
async function extractFromXml(file: File): Promise<string> {
  try {
    const content = await readTextFile(file);
    if (!content || content.trim().length === 0) {
      throw new Error('XML file appears to be empty');
    }

    // Simple XML text extraction (remove tags)
    let text = content.replace(/<[^>]*>/g, ' '); // Remove all XML tags
    text = text.replace(/\s+/g, ' ').trim(); // Clean up whitespace

    return text;
  } catch (error) {
    throw new Error(`Failed to process XML file: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Helper function to extract text from JSON objects
function extractTextFromJSON(obj: any, depth: number = 0): string {
  if (depth > 5) return ''; // Prevent infinite recursion

  let text = '';

  if (typeof obj === 'string') {
    text += obj + ' ';
  } else if (typeof obj === 'number' || typeof obj === 'boolean') {
    text += obj.toString() + ' ';
  } else if (Array.isArray(obj)) {
    obj.forEach(item => {
      text += extractTextFromJSON(item, depth + 1);
    });
  } else if (obj && typeof obj === 'object') {
    Object.values(obj).forEach(value => {
      text += extractTextFromJSON(value, depth + 1);
    });
  }

  return text;
}

// Process a single file
export async function processFile(
  file: File, 
  options: ProcessingOptions
): Promise<FileProcessingResult> {
  const startTime = Date.now();
  
  try {
    // Extract text from file
    const extractedText = await extractTextFromFile(file);
    
    if (!extractedText || extractedText.trim().length === 0) {
      throw new Error('No text content found in file');
    }
    
    // Process the extracted text
    const result = processTextDirectly(extractedText, options);
    
    return {
      fileName: file.name,
      originalSize: file.size,
      processedSize: result.humanizedText.length,
      result: result
    };
  } catch (error) {
    return {
      fileName: file.name,
      originalSize: file.size,
      processedSize: 0,
      result: {
        humanizedText: '',
        improvementScore: 0,
        detectionScore: 0,
        confidence: 0,
        readabilityScore: 0,
        processingTime: Date.now() - startTime,
        originalLength: 0,
        newLength: 0,
        variations: []
      },
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Process multiple files in batch
export async function processBatchFiles(
  files: File[], 
  options: ProcessingOptions,
  onProgress?: (completed: number, total: number) => void
): Promise<BatchProcessingResult> {
  const startTime = Date.now();
  const results: FileProcessingResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    const result = await processFile(file, options);
    results.push(result);
    
    // Report progress
    if (onProgress) {
      onProgress(i + 1, files.length);
    }
    
    // Add small delay to prevent UI blocking
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  const successfulFiles = results.filter(r => !r.error).length;
  const failedFiles = results.length - successfulFiles;
  const totalProcessingTime = Date.now() - startTime;
  
  // Calculate average improvement score (only for successful files)
  const successfulResults = results.filter(r => !r.error);
  const averageImprovementScore = successfulResults.length > 0
    ? successfulResults.reduce((sum, r) => sum + r.result.improvementScore, 0) / successfulResults.length
    : 0;
  
  return {
    results,
    totalFiles: files.length,
    successfulFiles,
    failedFiles,
    totalProcessingTime,
    averageImprovementScore
  };
}

// Validate file before processing
export function validateFile(file: File): { valid: boolean; error?: string } {
  // Check file size (max 50MB)
  if (file.size > 50 * 1024 * 1024) {
    return { valid: false, error: 'File size exceeds 50MB limit' };
  }

  // Check if file type is supported
  const fileType = file.type;
  const fileName = file.name.toLowerCase();

  const supportedExtensions = ['.txt', '.md', '.docx', '.pdf', '.rtf', '.odt', '.json', '.csv', '.html', '.htm', '.xml'];
  const isSupportedType = Object.keys(SUPPORTED_FILE_TYPES).includes(fileType) ||
    supportedExtensions.some(ext => fileName.endsWith(ext));

  if (!isSupportedType) {
    return { valid: false, error: `Unsupported file type. Supported formats: ${supportedExtensions.join(', ')}` };
  }

  return { valid: true };
}

// Generate download content for batch results
export function generateBatchResultsFile(batchResult: BatchProcessingResult): string {
  const timestamp = new Date().toISOString();
  let content = `GhostLayer Batch Processing Results\n`;
  content += `Generated: ${timestamp}\n`;
  content += `Total Files: ${batchResult.totalFiles}\n`;
  content += `Successful: ${batchResult.successfulFiles}\n`;
  content += `Failed: ${batchResult.failedFiles}\n`;
  content += `Average Improvement: ${batchResult.averageImprovementScore.toFixed(1)}%\n`;
  content += `Total Processing Time: ${(batchResult.totalProcessingTime / 1000).toFixed(2)}s\n\n`;
  
  content += `${'='.repeat(80)}\n\n`;
  
  batchResult.results.forEach((result, index) => {
    content += `File ${index + 1}: ${result.fileName}\n`;
    content += `Status: ${result.error ? 'FAILED' : 'SUCCESS'}\n`;
    
    if (result.error) {
      content += `Error: ${result.error}\n`;
    } else {
      content += `Improvement Score: ${result.result.improvementScore}%\n`;
      content += `Processing Time: ${result.result.processingTime}ms\n`;
      content += `Original Size: ${result.originalSize} bytes\n`;
      content += `Processed Size: ${result.processedSize} bytes\n\n`;
      content += `HUMANIZED CONTENT:\n`;
      content += `${'-'.repeat(40)}\n`;
      content += `${result.result.humanizedText}\n`;
    }
    
    content += `\n${'='.repeat(80)}\n\n`;
  });
  
  return content;
}
