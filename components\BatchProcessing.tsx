'use client';

import { useState, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Download,
  X,
  Zap,
  Clock,
  BarChart3,
  Trash2
} from 'lucide-react';
import { analytics } from '@/lib/analytics';
import { processBatchFiles, validateFile, generateBatchResultsFile, BatchProcessingResult } from '@/lib/fileProcessor';

interface FileStatus {
  file: File;
  status: 'pending' | 'processing' | 'completed' | 'error';
  error?: string;
  result?: any;
}

export default function BatchProcessing() {
  const { data: session } = useSession();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [files, setFiles] = useState<FileStatus[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingProgress, setProcessingProgress] = useState(0);
  const [batchResults, setBatchResults] = useState<BatchProcessingResult | null>(null);
  const [dragOver, setDragOver] = useState(false);

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles: FileStatus[] = [];
    
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      
      // Validate file
      const validation = validateFile(file);
      if (validation.valid) {
        newFiles.push({
          file,
          status: 'pending'
        });
      } else {
        newFiles.push({
          file,
          status: 'error',
          error: validation.error
        });
      }
    }

    setFiles(prev => [...prev, ...newFiles]);
    analytics.trackShare('batch_files_added', { count: newFiles.length });
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const clearAllFiles = () => {
    setFiles([]);
    setBatchResults(null);
    setProcessingProgress(0);
  };

  const processBatch = async () => {
    if (files.length === 0) return;

    const validFiles = files.filter(f => f.status !== 'error').map(f => f.file);
    if (validFiles.length === 0) {
      alert('No valid files to process');
      return;
    }

    setIsProcessing(true);
    setProcessingProgress(0);
    setBatchResults(null);

    // Update file statuses to processing
    setFiles(prev => prev.map(f => 
      f.status !== 'error' ? { ...f, status: 'processing' as const } : f
    ));

    try {
      const results = await processBatchFiles(
        validFiles,
        {
          intensity: 'medium',
          style: 'balanced',
          language: 'en'
        },
        (completed, total) => {
          setProcessingProgress((completed / total) * 100);
          
          // Update individual file statuses
          setFiles(prev => prev.map((f, index) => {
            if (f.status === 'error') return f;
            
            const fileIndex = validFiles.findIndex(vf => vf.name === f.file.name);
            if (fileIndex < completed) {
              return { ...f, status: 'completed' as const };
            }
            return f;
          }));
        }
      );

      setBatchResults(results);
      
      // Update final file statuses
      setFiles(prev => prev.map(f => {
        if (f.status === 'error') return f;
        
        const result = results.results.find(r => r.fileName === f.file.name);
        if (result?.error) {
          return { ...f, status: 'error' as const, error: result.error };
        }
        return { ...f, status: 'completed' as const, result };
      }));

      analytics.trackShare('batch_processing_completed', {
        filesProcessed: results.totalFiles,
        successfulFiles: results.successfulFiles,
        failedFiles: results.failedFiles
      });

    } catch (error) {
      console.error('Batch processing failed:', error);
      alert('Batch processing failed. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const downloadResults = () => {
    if (!batchResults) return;

    const content = generateBatchResultsFile(batchResults);
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `ghostlayer-batch-results-${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    analytics.trackShare('batch_results_downloaded');
  };

  const getStatusIcon = (status: FileStatus['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'processing':
        return <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-400" />;
    }
  };

  const getStatusColor = (status: FileStatus['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-600';
      case 'processing':
        return 'bg-blue-600';
      case 'completed':
        return 'bg-green-600';
      case 'error':
        return 'bg-red-600';
    }
  };

  if (!session) {
    return (
      <Card className="bg-white/5 backdrop-blur-lg border-white/10">
        <CardContent className="p-6 text-center">
          <AlertCircle className="w-12 h-12 text-yellow-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-white mb-2">Sign In Required</h3>
          <p className="text-gray-300">Please sign in to use batch processing.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <Card className="bg-white/5 backdrop-blur-lg border-white/10">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-white">
            <Upload className="w-5 h-5 text-purple-400" />
            Batch File Processing
            {files.length > 0 && (
              <Badge className="bg-purple-600 text-white">
                {files.length} files
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              dragOver
                ? 'border-purple-400 bg-purple-500/10'
                : 'border-gray-600 hover:border-gray-500'
            }`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-white mb-2">
              Drop files here or click to browse
            </h3>
            <p className="text-gray-300 mb-4">
              Supported formats: .txt, .md, .docx, .pdf, .rtf, .odt, .json, .csv, .html, .xml
            </p>
            <Button
              onClick={() => fileInputRef.current?.click()}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Upload className="w-4 h-4 mr-2" />
              Select Files
            </Button>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept=".txt,.md,.docx,.pdf,.rtf,.odt,.json,.csv,.html,.xml"
              onChange={(e) => handleFileSelect(e.target.files)}
              className="hidden"
            />
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card className="bg-white/5 backdrop-blur-lg border-white/10">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">Selected Files</CardTitle>
              <div className="flex gap-2">
                <Button
                  onClick={processBatch}
                  disabled={isProcessing || files.every(f => f.status === 'error')}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {isProcessing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      <Zap className="w-4 h-4 mr-2" />
                      Process All
                    </>
                  )}
                </Button>
                <Button
                  onClick={clearAllFiles}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-800"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Clear All
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isProcessing && (
              <div className="mb-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-white text-sm">Processing Progress</span>
                  <span className="text-gray-300 text-sm">{Math.round(processingProgress)}%</span>
                </div>
                <Progress value={processingProgress} className="w-full" />
              </div>
            )}
            
            <div className="space-y-2">
              {files.map((fileStatus, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-800/30 rounded-lg border border-gray-700"
                >
                  <div className="flex items-center gap-3 flex-1">
                    {getStatusIcon(fileStatus.status)}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="text-white font-medium">{fileStatus.file.name}</span>
                        <Badge className={`${getStatusColor(fileStatus.status)} text-white text-xs`}>
                          {fileStatus.status}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-400">
                        <span>{(fileStatus.file.size / 1024).toFixed(1)} KB</span>
                        <span>{fileStatus.file.type || 'Unknown type'}</span>
                      </div>
                      {fileStatus.error && (
                        <p className="text-red-400 text-sm mt-1">{fileStatus.error}</p>
                      )}
                    </div>
                  </div>
                  <Button
                    onClick={() => removeFile(index)}
                    variant="ghost"
                    size="sm"
                    className="text-gray-400 hover:text-red-400"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Results Summary */}
      {batchResults && (
        <Card className="bg-white/5 backdrop-blur-lg border-white/10">
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="text-white">Processing Results</CardTitle>
              <Button
                onClick={downloadResults}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Download className="w-4 h-4 mr-2" />
                Download Report
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">
                  {batchResults.totalFiles}
                </div>
                <div className="text-gray-400 text-sm">Total Files</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">
                  {batchResults.successfulFiles}
                </div>
                <div className="text-gray-400 text-sm">Successful</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-400">
                  {batchResults.failedFiles}
                </div>
                <div className="text-gray-400 text-sm">Failed</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-400">
                  {batchResults.averageImprovementScore.toFixed(1)}%
                </div>
                <div className="text-gray-400 text-sm">Avg Improvement</div>
              </div>
            </div>
            
            <div className="p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <BarChart3 className="w-4 h-4 text-green-400" />
                <span className="text-green-300 font-medium">Batch Processing Complete</span>
              </div>
              <p className="text-green-200 text-sm">
                Processed {batchResults.totalFiles} files in {(batchResults.totalProcessingTime / 1000).toFixed(2)} seconds.
                {batchResults.failedFiles > 0 && ` ${batchResults.failedFiles} files failed to process.`}
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
