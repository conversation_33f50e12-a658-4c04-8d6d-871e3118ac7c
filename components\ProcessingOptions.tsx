'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Card } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Settings, Zap, Palette, Shield, Crown, Lock, Copy, Check } from 'lucide-react';
import { analytics } from '@/lib/analytics';
import { shareRewardManager } from '@/lib/shareRewards';
import ViralReferralSystem from './ViralReferralSystem';

interface ProcessingOptionsProps {
  options: {
    intensity: string;
    style: string;
    preserveFormat: boolean;
    addVariations: boolean;
  };
  onChange: (options: any) => void;
}

export default function ProcessingOptions({ options, onChange }: ProcessingOptionsProps) {
  const { data: session } = useSession();
  const isPremium = session?.user?.tier === 'premium' || session?.user?.tier === 'pro';

  // Referral state
  const [referralCode, setReferralCode] = useState('');
  const [copied, setCopied] = useState(false);
  const [showViralReferral, setShowViralReferral] = useState(false);

  // Check for share-based premium access
  const hasSharePremium = session?.user?.id ? shareRewardManager.hasPremiumAccess(session.user.id) : false;
  const effectivelyPremium = isPremium || hasSharePremium;

  // Initialize referral code when component mounts or user changes
  useEffect(() => {
    if (session?.user?.id) {
      const userReferralCode = `GHOST-${session.user.id.slice(-6).toUpperCase()}`;
      setReferralCode(userReferralCode);
    } else {
      setReferralCode('GHOST-DEMO123');
    }
  }, [session?.user?.id]);
  // Fixed positioning logic: Always use 0-100 scale for consistent visual positioning
  const getIntensityValue = () => {
    // Always use consistent visual positioning regardless of user type
    switch (options.intensity) {
      case 'light': return 0;    // 0% - far left
      case 'medium': return 50;  // 50% - exact center
      case 'heavy': return 100;  // 100% - far right
      default: return 50;        // default to medium
    }
  };

  const intensityValue = getIntensityValue();

  const handleIntensityChange = (value: number[]) => {
    const newValue = value[0];
    let intensity: string;

    // Use consistent boundaries for all users based on visual position
    if (newValue <= 25) {
      intensity = 'light';
    } else if (newValue <= 75) {
      intensity = 'medium';
    } else {
      intensity = 'heavy';
    }

    // Restrict heavy mode to premium users
    if (intensity === 'heavy' && !effectivelyPremium) {
      // For non-premium users, if they try to go beyond medium, keep it at medium
      onChange({ ...options, intensity: 'medium' });
      return;
    }

    onChange({ ...options, intensity });
  };

  // Copy referral link to clipboard
  const copyReferralLink = async () => {
    try {
      const referralLink = `https://ghostlayer.app/ref/${referralCode}`;
      await navigator.clipboard.writeText(referralLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      analytics.trackShare('referral_code_copied');
    } catch (error) {
      console.error('Failed to copy:', error);
    }
  };

  return (
    <>
    <Card className="p-6 bg-white/5 backdrop-blur-lg border-white/10">
      <h3 className="text-lg font-semibold text-white mb-6 flex items-center gap-2">
        <Settings className="w-5 h-5" />
        Processing Options
      </h3>
      
      <div className="space-y-6">
        <div>
          <Label className="text-white mb-3 block flex items-center gap-2">
            <Zap className="w-4 h-4 text-yellow-400" />
            Transformation Intensity
          </Label>
          <div className="px-2">
            <Slider
              value={[intensityValue]}
              onValueChange={handleIntensityChange}
              max={100}
              step={1}
              className="w-full"
            />
            {/* Enhanced slider labels with proper positioning */}
            <div className="relative mt-3">
              <div className="flex justify-between items-center text-xs">
                {/* Light label - positioned at 0% */}
                <div className="flex flex-col items-center">
                  <span className={`font-medium transition-colors duration-200 ${
                    options.intensity === 'light'
                      ? 'text-blue-400 scale-110'
                      : 'text-white hover:text-blue-300'
                  }`}>
                    Light
                  </span>
                  {options.intensity === 'light' && (
                    <div className="w-2 h-2 bg-blue-400 rounded-full mt-1 animate-pulse"></div>
                  )}
                </div>

                {/* Medium label - positioned at 50% */}
                <div className="flex flex-col items-center absolute left-1/2 transform -translate-x-1/2">
                  <span className={`font-medium transition-colors duration-200 ${
                    options.intensity === 'medium'
                      ? 'text-purple-400 scale-110'
                      : 'text-white hover:text-purple-300'
                  }`}>
                    Medium
                  </span>
                  {options.intensity === 'medium' && (
                    <div className="w-2 h-2 bg-purple-400 rounded-full mt-1 animate-pulse"></div>
                  )}
                </div>

                {/* Heavy label - positioned at 100% */}
                <div className="flex flex-col items-center">
                  <div className="flex items-center gap-1">
                    <span className={`font-medium transition-colors duration-200 ${
                      !effectivelyPremium
                        ? 'text-gray-500'
                        : options.intensity === 'heavy'
                          ? 'text-yellow-400 scale-110'
                          : 'text-white hover:text-yellow-300'
                    }`}>
                      Heavy
                    </span>
                    {!effectivelyPremium && (
                      <div className="flex items-center gap-1">
                        <Lock className="w-3 h-3 text-yellow-400" />
                        <Badge className="bg-yellow-500/20 text-yellow-400 text-xs px-1 py-0">
                          <Crown className="w-2 h-2 mr-1" />
                          Premium
                        </Badge>
                      </div>
                    )}
                    {hasSharePremium && !isPremium && (
                      <Badge className="bg-blue-500/20 text-blue-400 text-xs px-1 py-0 ml-1">
                        Share Unlock
                      </Badge>
                    )}
                  </div>
                  {options.intensity === 'heavy' && effectivelyPremium && (
                    <div className="w-2 h-2 bg-yellow-400 rounded-full mt-1 animate-pulse"></div>
                  )}
                </div>
              </div>
            </div>
          </div>
          <p className="text-xs text-gray-400 mt-2">
            {options.intensity === 'light' && 'Minimal changes, preserves original structure'}
            {options.intensity === 'medium' && 'Balanced transformation with good readability'}
            {options.intensity === 'heavy' && 'Maximum humanization, significant restructuring'}
          </p>
          {!isPremium && (
            <div className="mt-3 p-3 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-400 text-sm">
                <Crown className="w-4 h-4" />
                <span className="font-medium">Upgrade to Premium</span>
              </div>
              <div className="text-xs text-gray-300 mt-1 space-y-3">
                <p>Unlock Heavy intensity mode and advanced humanization features.</p>

                {/* Inline Referral Section */}
                <div className="bg-gray-800/30 rounded-lg p-3 border border-gray-700">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-yellow-400 font-medium text-sm">
                      🎁 Refer Friends & Earn Credits
                    </span>
                    <Button
                      onClick={() => setShowViralReferral(true)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-400 hover:text-white text-xs"
                    >
                      Learn More
                    </Button>
                  </div>

                  <div className="flex items-center gap-2">
                    <div className="flex-1">
                      <div className="text-xs text-gray-500 mb-1">Your referral code:</div>
                      <div className="font-mono text-sm text-white bg-gray-900/50 px-2 py-1 rounded border">
                        {referralCode}
                      </div>
                    </div>
                    <Button
                      onClick={copyReferralLink}
                      size="sm"
                      className={`transition-all duration-200 ${
                        copied
                          ? 'bg-green-600 hover:bg-green-700'
                          : 'bg-blue-600 hover:bg-blue-700'
                      }`}
                    >
                      {copied ? (
                        <>
                          <Check className="w-4 h-4 mr-1" />
                          Copied!
                        </>
                      ) : (
                        <>
                          <Copy className="w-4 h-4 mr-1" />
                          Copy Link
                        </>
                      )}
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500 mt-2">
                    Share your link to earn 100 credits per referral • Friends get 50 credits
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        <div>
          <Label className="text-white mb-3 block flex items-center gap-2">
            <Palette className="w-4 h-4 text-purple-400" />
            Writing Style
          </Label>
          <Select value={options.style} onValueChange={(value) => {
            analytics.trackStyleChange(options.style, value);
            onChange({ ...options, style: value });
          }}>
            <SelectTrigger className="bg-slate-800/50 border-slate-700 text-white">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="bg-slate-800 border-slate-700 text-white">
              <SelectItem value="academic" className="text-white hover:bg-blue-600 focus:bg-blue-600">Academic (Recommended)</SelectItem>
              <SelectItem value="balanced" className="text-white hover:bg-blue-600 focus:bg-blue-600">Balanced</SelectItem>
              <SelectItem value="formal" className="text-white hover:bg-blue-600 focus:bg-blue-600">Formal</SelectItem>
              <SelectItem value="casual" className="text-white hover:bg-blue-600 focus:bg-blue-600">Casual</SelectItem>
              <SelectItem value="creative" className="text-white hover:bg-blue-600 focus:bg-blue-600">Creative</SelectItem>
              <SelectItem value="technical" className="text-white hover:bg-blue-600 focus:bg-blue-600">Technical</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white flex items-center gap-2">
                <Shield className="w-4 h-4 text-green-400" />
                Preserve Formatting
              </Label>
              <p className="text-xs text-gray-400 mt-1">
                Keep original paragraph structure and formatting
              </p>
            </div>
            <Switch
              checked={options.preserveFormat}
              onCheckedChange={(checked) => onChange({ ...options, preserveFormat: checked })}
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <Label className="text-white">Generate Variations</Label>
              <p className="text-xs text-gray-400 mt-1">
                Create multiple versions for comparison
              </p>
            </div>
            <Switch
              checked={options.addVariations}
              onCheckedChange={(checked) => {
                analytics.trackVariationsToggle(checked);
                onChange({ ...options, addVariations: checked });
              }}
            />
          </div>
        </div>
      </div>
    </Card>



    {/* Viral Referral System Modal */}
    <ViralReferralSystem
      isOpen={showViralReferral}
      onClose={() => setShowViralReferral(false)}
    />
    </>
  );
}